import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { 
  <PERSON>, 
  MessageCircle, 
  Shuffle, 
  Sparkles, 
  TrendingUp, 
  Heart, 
  Star, 
  Crown, 
  Zap,
  Target,
  Award,
  Users,
  Calendar,
  BarChart3,
  Settings,
  LogOut,
  Clock
} from 'lucide-react';
import { Page } from '../../types';
import { GlassCard } from '../../components/glass-card';
import { FluidBlob } from '../../components/fluid-blob';
import { LiquidButton } from '../../components/liquid-glass-button';
import { GradientButton } from '../../components/gradient-button';
import { GlowingEffect } from '../../components/glowing-effect';
import { VapourTextEffect } from '../../components/vapour-text-effect';

interface DashboardPageProps {
  onNavigate: (page: Page) => void;
  onLogout: () => void;
}

const DashboardPage: React.FC<DashboardPageProps> = ({ onNavigate, onLogout }) => {
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });
  const [isLoaded, setIsLoaded] = useState(false);
  const [stats, setStats] = useState({
    conversas: 47,
    sucessos: 23,
    temas: 156,
    pontuacao: 2847
  });

  useEffect(() => {
    setIsLoaded(true);
    
    const handleMouseMove = (e: MouseEvent) => {
      setMousePosition({ x: e.clientX, y: e.clientY });
    };

    window.addEventListener('mousemove', handleMouseMove);
    return () => window.removeEventListener('mousemove', handleMouseMove);
  }, []);

  const quickActions = [
    {
      title: 'Chat IA',
      description: 'Converse com nossa IA',
      icon: MessageCircle,
      color: 'blue',
      action: () => onNavigate('chat')
    },
    {
      title: 'Roleta',
      description: 'Gire e descubra temas',
      icon: Shuffle,
      color: 'orange',
      action: () => onNavigate('roleta')
    },
    {
      title: 'Histórias',
      description: 'Suas conversas salvas',
      icon: Heart,
      color: 'pink',
      action: () => onNavigate('stories')
    }
  ];

  const achievements = [
    { title: 'Primeiro Papo', icon: Star, unlocked: true },
    { title: 'Conversador', icon: MessageCircle, unlocked: true },
    { title: 'Especialista', icon: Crown, unlocked: false },
    { title: 'Mestre', icon: Award, unlocked: false }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-black to-gray-800 relative overflow-hidden">
      {/* Animated background with 4D effects */}
      <div className="absolute inset-0 overflow-hidden">
        <FluidBlob 
          size="xl" 
          color="gradient" 
          animated={true}
          className="absolute top-10 left-10 opacity-20" 
        />
        <FluidBlob 
          size="lg" 
          color="cyan"
          animated={true}
          className="absolute top-1/3 right-20 opacity-15" 
        />
        <FluidBlob 
          size="md" 
          color="purple" 
          animated={true}
          className="absolute bottom-20 left-1/4 opacity-25" 
        />
        
        {/* Floating particles */}
        <div className="absolute inset-0 pointer-events-none">
          {[...Array(30)].map((_, i) => (
            <motion.div
              key={i}
              className="absolute w-1 h-1 bg-blue-400/30 rounded-full"
              style={{
                left: `${Math.random() * 100}%`,
                top: `${Math.random() * 100}%`
              }}
              animate={{
                y: [-20, 20, -20],
                x: [-10, 10, -10],
                opacity: [0.3, 0.8, 0.3],
                scale: [0.5, 1.5, 0.5]
              }}
              transition={{
                duration: 4 + i * 0.2,
                repeat: Infinity,
                ease: "easeInOut"
              }}
            />
          ))}
        </div>
      </div>

      {/* Mouse follower effect */}
      <motion.div
        className="fixed w-96 h-96 rounded-full bg-blue-500/10 blur-3xl pointer-events-none z-0"
        style={{
          left: mousePosition.x - 192,
          top: mousePosition.y - 192
        }}
        animate={{
          scale: [1, 1.2, 1],
          opacity: [0.1, 0.3, 0.1]
        }}
        transition={{
          duration: 3,
          repeat: Infinity,
          ease: "easeInOut"
        }}
      />

      <div className="relative z-10 container mx-auto px-6 py-8">
        {/* Header */}
        <motion.header
          className="flex justify-between items-center mb-12"
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: isLoaded ? 1 : 0, y: isLoaded ? 0 : -20 }}
          transition={{ duration: 0.8 }}
        >
          <div className="flex items-center gap-4">
            <GlowingEffect color="orange" size="lg" className="inline-block">
              <Flame className="w-10 h-10 text-orange-400" />
            </GlowingEffect>
            <div>
              <VapourTextEffect 
                texts={["PapoFire"]}
                color="fire" 
                size="2xl" 
                className="font-bold"
              />
              <p className="text-gray-400 text-sm">Dashboard</p>
            </div>
          </div>

          <div className="flex gap-3">
            <LiquidButton 
              variant="secondary" 
              size="lg"
            >
              <Settings className="w-5 h-5" />
            </LiquidButton>
            <LiquidButton 
              variant="ghost" 
              size="lg"
              onClick={onLogout}
            >
              <LogOut className="w-5 h-5" />
            </LiquidButton>
          </div>
        </motion.header>

        {/* Welcome Section */}
        <motion.div
          className="mb-12"
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
        >
          <GlassCard
            variant="holographic"
            size="xl"
            glow={true}
            floating={true}
            className="text-center relative overflow-hidden"
          >
            {/* Animated background particles */}
            <div className="absolute inset-0 pointer-events-none">
              {[...Array(12)].map((_, i) => (
                <motion.div
                  key={i}
                  className="absolute w-1 h-1 bg-orange-400/40 rounded-full"
                  style={{
                    left: `${Math.random() * 100}%`,
                    top: `${Math.random() * 100}%`
                  }}
                  animate={{
                    y: [-8, 8, -8],
                    x: [-4, 4, -4],
                    opacity: [0.4, 0.8, 0.4],
                    scale: [0.5, 1, 0.5]
                  }}
                  transition={{
                    duration: 2 + i * 0.1,
                    repeat: Infinity,
                    ease: "easeInOut"
                  }}
                />
              ))}
            </div>

            <div className="relative z-10">
              <GlowingEffect 
                color="blue" 
                size="lg" 
                className="mb-4 inline-block"
                pulsing={true}
              >
                <Sparkles className="w-8 h-8" />
              </GlowingEffect>

              <VapourTextEffect 
                texts={["Bem-vindo de volta!"]}
                color="ice" 
                size="2xl" 
                className="mb-2"
              />

              <p className="text-gray-300 mb-6">
                Pronto para mais conversas incríveis? Vamos começar!
              </p>

              <GradientButton
                variant="cosmic"
                size="lg"
                onClick={() => onNavigate('chat')}
              >
                <Zap className="w-5 h-5 mr-2" />
                Começar Agora
              </GradientButton>
            </div>
          </GlassCard>
        </motion.div>

        {/* Stats Grid */}
        <motion.div
          className="grid grid-cols-2 lg:grid-cols-4 gap-6 mb-12"
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
        >
          {[
            { label: 'Conversas', value: stats.conversas, icon: MessageCircle, color: 'blue' },
            { label: 'Sucessos', value: stats.sucessos, icon: Heart, color: 'pink' },
            { label: 'Temas', value: stats.temas, icon: Target, color: 'green' },
            { label: 'Pontuação', value: stats.pontuacao, icon: Star, color: 'yellow' }
          ].map((stat, index) => (
            <motion.div
              key={stat.label}
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ delay: 0.5 + index * 0.1 }}
              whileHover={{ scale: 1.05, rotateY: 5 }}
            >
              <GlassCard
                size="lg"
                glow={true}
                floating={true}
                className="text-center"
              >
                <GlowingEffect 
                  color={stat.color as any} 
                  size="md" 
                  className="mb-3 inline-block"
                >
                  <stat.icon className="w-6 h-6" />
                </GlowingEffect>

                <VapourTextEffect 
                  texts={[stat.value.toString()]}
                  color="ice"
                  size="xl"
                  className="font-bold mb-1"
                />

                <p className="text-gray-400 text-sm">{stat.label}</p>
              </GlassCard>
            </motion.div>
          ))}
        </motion.div>

        {/* Quick Actions */}
        <motion.div
          className="mb-12"
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.6 }}
        >
          <div className="mb-6">
            <VapourTextEffect 
              texts={["🚀 Ações Rápidas"]}
              color="fire" 
              size="xl" 
              className="mb-2"
            />
            <p className="text-gray-400">Escolha uma opção para começar</p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {quickActions.map((action, index) => (
              <motion.div
                key={action.title}
                initial={{ opacity: 0, y: 30 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.7 + index * 0.1 }}
                whileHover={{ scale: 1.05, rotateY: 5 }}
              >
                <GlassCard
                  variant="holographic"
                  size="xl"
                  glow={true}
                  floating={true}
                  className="text-center cursor-pointer"
                  onClick={action.action}
                >
                  <GlowingEffect 
                    color={action.color as any} 
                    size="lg" 
                    className="mb-4 inline-block"
                    pulsing={true}
                  >
                    <action.icon className="w-8 h-8" />
                  </GlowingEffect>

                  <VapourTextEffect 
                    texts={[action.title]}
                    color="cosmic"
                    size="lg"
                    className="mb-2 font-bold"
                  />

                  <p className="text-gray-300 mb-4">{action.description}</p>

                  <GradientButton
                    variant={action.color as any}
                    size="md"
                    className="w-full"
                  >
                    Acessar
                  </GradientButton>
                </GlassCard>
              </motion.div>
            ))}
          </div>
        </motion.div>

        {/* Progress & Achievements */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-12">
          {/* Progress Chart */}
          <motion.div
            initial={{ opacity: 0, x: -30 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.8 }}
          >
            <GlassCard
              size="xl"
              glow={true}
              floating={true}
            >
              <div className="mb-4">
                <VapourTextEffect 
                  texts={["📊 Seu Progresso"]}
                  color="nature" 
                  size="lg" 
                  className="mb-2"
                />
                <p className="text-gray-400 text-sm">Evolução nas últimas semanas</p>
              </div>

              <div className="space-y-4">
                {[
                  { label: 'Conversas', progress: 75, color: 'blue' },
                  { label: 'Sucessos', progress: 60, color: 'green' },
                  { label: 'Engajamento', progress: 85, color: 'purple' }
                ].map((item, index) => (
                  <div key={item.label} className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-300">{item.label}</span>
                      <span className="text-gray-400">{item.progress}%</span>
                    </div>
                    <div className="w-full bg-gray-700 rounded-full h-2">
                      <motion.div
                        className={`h-2 rounded-full bg-gradient-to-r ${
                          item.color === 'blue' ? 'from-blue-500 to-cyan-500' :
                          item.color === 'green' ? 'from-green-500 to-emerald-500' :
                          'from-purple-500 to-pink-500'
                        }`}
                        initial={{ width: 0 }}
                        animate={{ width: `${item.progress}%` }}
                        transition={{ delay: 1 + index * 0.2, duration: 1 }}
                      />
                    </div>
                  </div>
                ))}
              </div>
            </GlassCard>
          </motion.div>

          {/* Achievements */}
          <motion.div
            initial={{ opacity: 0, x: 30 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.9 }}
          >
            <GlassCard
              size="xl"
              glow={true}
              floating={true}
            >
              <div className="mb-4">
                <VapourTextEffect 
                  texts={["🏆 Conquistas"]}
                  color="fire" 
                  size="lg" 
                  className="mb-2"
                />
                <p className="text-gray-400 text-sm">Seus marcos alcançados</p>
              </div>

              <div className="space-y-3">
                {achievements.map((achievement, index) => (
                  <motion.div
                    key={achievement.title}
                    className={`flex items-center gap-3 p-3 rounded-lg ${
                      achievement.unlocked 
                        ? 'bg-green-500/20 border border-green-500/30' 
                        : 'bg-gray-700/20 border border-gray-600/30'
                    }`}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: 1.1 + index * 0.1 }}
                  >
                    <GlowingEffect 
                      color={achievement.unlocked ? "green" : "orange"}
                      size="sm"
                    >
                      <achievement.icon className={`w-5 h-5 ${
                        achievement.unlocked ? 'text-green-400' : 'text-gray-500'
                      }`} />
                    </GlowingEffect>
                    
                    <span className={`font-medium ${
                      achievement.unlocked ? 'text-green-300' : 'text-gray-500'
                    }`}>
                      {achievement.title}
                    </span>

                    {achievement.unlocked && (
                      <motion.div
                        initial={{ scale: 0 }}
                        animate={{ scale: 1 }}
                        transition={{ delay: 1.2 + index * 0.1 }}
                      >
                        <Star className="w-4 h-4 text-yellow-400 ml-auto" />
                      </motion.div>
                    )}
                  </motion.div>
                ))}
              </div>
            </GlassCard>
          </motion.div>
        </div>

        {/* Recent Activity */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 1.0 }}
        >
          <GlassCard
            size="xl"
            glow={true}
            floating={true}
          >
            <div className="mb-6">
              <VapourTextEffect 
                texts={["📈 Atividade Recente"]}
                color="cosmic" 
                size="lg" 
                className="mb-2"
              />
              <p className="text-gray-400 text-sm">Suas últimas interações</p>
            </div>

            <div className="space-y-4">
              {[
                { action: 'Conversa com IA sobre viagens', time: '2 horas atrás', success: true },
                { action: 'Tema da roleta: Comida favorita', time: '5 horas atrás', success: true },
                { action: 'História salva: Primeiro encontro', time: '1 dia atrás', success: false },
                { action: 'Chat sobre hobbies', time: '2 dias atrás', success: true }
              ].map((activity, index) => (
                <motion.div
                  key={index}
                  className="flex items-center gap-4 p-4 bg-gray-800/30 rounded-lg border border-gray-700/30"
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: 1.2 + index * 0.1 }}
                  whileHover={{ scale: 1.02, backgroundColor: 'rgba(55, 65, 81, 0.4)' }}
                >
                  <div className={`w-3 h-3 rounded-full ${
                    activity.success ? 'bg-green-400' : 'bg-yellow-400'
                  }`} />
                  
                  <div className="flex-1">
                    <p className="text-gray-200 font-medium">{activity.action}</p>
                    <p className="text-gray-400 text-sm">{activity.time}</p>
                  </div>

                  <GlowingEffect color={activity.success ? "green" : "orange"} size="sm">
                    {activity.success ? (
                      <Heart className="w-4 h-4" />
                    ) : (
                      <Clock className="w-4 h-4" />
                    )}
                  </GlowingEffect>
                </motion.div>
              ))}
            </div>
          </GlassCard>
        </motion.div>
      </div>
    </div>
  );
};

export default DashboardPage;
export { DashboardPage as HomePage };
