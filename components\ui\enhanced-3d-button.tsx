import React, { useState, useRef, useEffect } from 'react';
import { motion, useMotionValue, useSpring, useTransform } from 'framer-motion';
import { cn } from '../../lib/utils';

interface Enhanced3DButtonProps {
  children: React.ReactNode;
  onClick?: (e: React.MouseEvent<HTMLButtonElement>) => void;
  variant?: 'primary' | 'secondary' | 'accent' | 'holographic' | 'neon' | 'liquid';
  size?: 'sm' | 'md' | 'lg' | 'xl';
  disabled?: boolean;
  className?: string;
  glow?: boolean;
  particles?: boolean;
  ripple?: boolean;
  type?: 'button' | 'submit' | 'reset';
}

export const Enhanced3DButton: React.FC<Enhanced3DButtonProps> = ({
  children,
  onClick,
  variant = 'primary',
  size = 'md',
  disabled = false,
  className = '',
  glow = true,
  particles = false,
  ripple = true,
  type = 'button',
}) => {
  const [isHovered, setIsHovered] = useState(false);
  const [isPressed, setIsPressed] = useState(false);
  const [ripples, setRipples] = useState<Array<{ id: number; x: number; y: number }>>([]);
  const buttonRef = useRef<HTMLButtonElement>(null);

  const mouseX = useMotionValue(0);
  const mouseY = useMotionValue(0);
  const rotateX = useSpring(useTransform(mouseY, [-0.5, 0.5], [10, -10]));
  const rotateY = useSpring(useTransform(mouseX, [-0.5, 0.5], [-10, 10]));

  const variants = {
    primary: {
      base: 'bg-gradient-to-br from-orange-500 to-red-600 text-white',
      glow: 'shadow-orange-500/50',
      hover: 'from-orange-400 to-red-500',
    },
    secondary: {
      base: 'bg-gradient-to-br from-purple-500 to-pink-600 text-white',
      glow: 'shadow-purple-500/50',
      hover: 'from-purple-400 to-pink-500',
    },
    accent: {
      base: 'bg-gradient-to-br from-cyan-500 to-blue-600 text-white',
      glow: 'shadow-cyan-500/50',
      hover: 'from-cyan-400 to-blue-500',
    },
    holographic: {
      base: 'bg-gradient-to-br from-pink-500 via-purple-500 to-cyan-500 text-white',
      glow: 'shadow-pink-500/50',
      hover: 'from-pink-400 via-purple-400 to-cyan-400',
    },
    neon: {
      base: 'bg-gradient-to-br from-green-400 to-emerald-600 text-white',
      glow: 'shadow-green-500/50',
      hover: 'from-green-300 to-emerald-500',
    },
    liquid: {
      base: 'bg-gradient-to-br from-indigo-500 to-purple-600 text-white',
      glow: 'shadow-indigo-500/50',
      hover: 'from-indigo-400 to-purple-500',
    },
  };

  const sizes = {
    sm: 'px-4 py-2 text-sm rounded-lg',
    md: 'px-6 py-3 text-base rounded-xl',
    lg: 'px-8 py-4 text-lg rounded-2xl',
    xl: 'px-10 py-5 text-xl rounded-3xl',
  };

  const handleMouseMove = (e: React.MouseEvent<HTMLButtonElement>) => {
    if (!buttonRef.current) return;
    
    const rect = buttonRef.current.getBoundingClientRect();
    const x = (e.clientX - rect.left - rect.width / 2) / rect.width;
    const y = (e.clientY - rect.top - rect.height / 2) / rect.height;
    
    mouseX.set(x);
    mouseY.set(y);
  };

  const handleClick = (e: React.MouseEvent<HTMLButtonElement>) => {
    if (disabled) return;

    // Create ripple effect
    if (ripple && buttonRef.current) {
      const rect = buttonRef.current.getBoundingClientRect();
      const x = e.clientX - rect.left;
      const y = e.clientY - rect.top;
      const newRipple = { id: Date.now(), x, y };
      
      setRipples(prev => [...prev, newRipple]);
      
      setTimeout(() => {
        setRipples(prev => prev.filter(ripple => ripple.id !== newRipple.id));
      }, 600);
    }

    onClick?.(e);
  };

  const currentVariant = variants[variant];

  return (
    <motion.button
      ref={buttonRef}
      type={type}
      className={cn(
        'relative overflow-hidden font-semibold transition-all duration-300 transform-3d',
        'border-0 outline-none focus:outline-none',
        'disabled:opacity-50 disabled:cursor-not-allowed',
        currentVariant.base,
        sizes[size],
        glow && !disabled && `shadow-2xl ${currentVariant.glow}`,
        className
      )}
      style={{
        transformStyle: 'preserve-3d',
        rotateX,
        rotateY,
      }}
      initial={{ scale: 1 }}
      whileHover={!disabled ? { 
        scale: 1.05,
        boxShadow: glow ? '0 0 40px rgba(255, 61, 0, 0.6)' : undefined,
      } : {}}
      whileTap={!disabled ? { scale: 0.95 } : {}}
      onMouseMove={handleMouseMove}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => {
        setIsHovered(false);
        mouseX.set(0);
        mouseY.set(0);
      }}
      onMouseDown={() => setIsPressed(true)}
      onMouseUp={() => setIsPressed(false)}
      onClick={handleClick}
      disabled={disabled}
    >
      {/* Background gradient overlay */}
      <motion.div
        className={cn(
          'absolute inset-0 opacity-0 transition-opacity duration-300',
          `bg-gradient-to-br ${currentVariant.hover}`
        )}
        animate={{ opacity: isHovered && !disabled ? 1 : 0 }}
      />

      {/* Holographic shimmer effect */}
      {variant === 'holographic' && (
        <motion.div
          className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent"
          animate={{
            x: isHovered ? ['-100%', '100%'] : '-100%',
          }}
          transition={{
            duration: 1.5,
            ease: 'easeInOut',
            repeat: isHovered ? Infinity : 0,
          }}
        />
      )}

      {/* Liquid animation effect */}
      {variant === 'liquid' && (
        <motion.div
          className="absolute inset-0 bg-gradient-to-br from-indigo-400/30 to-purple-500/30 rounded-inherit"
          animate={{
            scale: isHovered ? [1, 1.1, 1] : 1,
            rotate: isHovered ? [0, 5, -5, 0] : 0,
          }}
          transition={{
            duration: 2,
            ease: 'easeInOut',
            repeat: isHovered ? Infinity : 0,
          }}
        />
      )}

      {/* Neon glow effect */}
      {variant === 'neon' && isHovered && (
        <motion.div
          className="absolute inset-0 bg-green-400/20 rounded-inherit blur-sm"
          animate={{
            scale: [1, 1.2, 1],
            opacity: [0.5, 1, 0.5],
          }}
          transition={{
            duration: 1,
            ease: 'easeInOut',
            repeat: Infinity,
          }}
        />
      )}

      {/* Ripple effects */}
      {ripples.map((ripple) => (
        <motion.div
          key={ripple.id}
          className="absolute bg-white/30 rounded-full pointer-events-none"
          style={{
            left: ripple.x - 10,
            top: ripple.y - 10,
            width: 20,
            height: 20,
          }}
          initial={{ scale: 0, opacity: 1 }}
          animate={{ scale: 4, opacity: 0 }}
          transition={{ duration: 0.6, ease: 'easeOut' }}
        />
      ))}

      {/* Particle effects */}
      {particles && isHovered && (
        <div className="absolute inset-0 pointer-events-none">
          {[...Array(8)].map((_, i) => (
            <motion.div
              key={i}
              className="absolute w-1 h-1 bg-white rounded-full"
              style={{
                left: `${Math.random() * 100}%`,
                top: `${Math.random() * 100}%`,
              }}
              animate={{
                y: [0, -20],
                opacity: [0, 1, 0],
                scale: [0, 1, 0],
              }}
              transition={{
                duration: 1,
                delay: i * 0.1,
                repeat: Infinity,
                repeatDelay: 1,
              }}
            />
          ))}
        </div>
      )}

      {/* Content with 3D transform */}
      <motion.span
        className="relative z-10 block"
        style={{
          transform: isPressed ? 'translateZ(-5px)' : 'translateZ(0px)',
        }}
        transition={{ duration: 0.1 }}
      >
        {children}
      </motion.span>

      {/* 3D depth effect */}
      <motion.div
        className="absolute inset-0 bg-black/20 rounded-inherit"
        style={{
          transform: 'translateZ(-10px)',
        }}
        animate={{
          opacity: isPressed ? 0.5 : 0.2,
        }}
        transition={{ duration: 0.1 }}
      />
    </motion.button>
  );
};

export default Enhanced3DButton;
