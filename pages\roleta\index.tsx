import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { <PERSON>, Spark<PERSON>, Shuffle, Play, Pause, RotateCcw, <PERSON><PERSON>, Star, Heart, Crown } from 'lucide-react';
import { Page } from '../../types';
import { GlassCard } from '../../components/glass-card';
import { FluidBlob } from '../../components/fluid-blob';
import { LiquidButton } from '../../components/liquid-glass-button';
import { GradientButton } from '../../components/gradient-button';
import { GlowingEffect } from '../../components/glowing-effect';
import { VapourTextEffect } from '../../components/vapour-text-effect';

interface RoletaPageProps {
  onBack: () => void;
}

interface RoletaTemasProps {
  setSelectedTema: (tema: string) => void;
  isSpinning: boolean;
  setIsSpinning: (spinning: boolean) => void;
}

const temas = [
  "Viagem dos sonhos 🌍",
  "Comida favorita 🍕",
  "Filme que marcou 🎬",
  "Música que emociona 🎵",
  "Hobby secreto 🎨",
  "Lugar especial 🏞️",
  "Memória de infância 👶",
  "Sonho maluco 💭",
  "Animal de estimação 🐕",
  "Superpoder desejado 🦸",
  "Livro inesquecível 📚",
  "Receita da vovó 👵",
  "Aventura radical 🏄",
  "Talento oculto 🎭",
  "Medo bobo 😱",
  "Paixão platônica 💕",
  "Trabalho dos sonhos 💼",
  "Época histórica 🏛️",
  "Idioma para aprender 🗣️",
  "Invenção genial 💡"
];

const RoletaTemas: React.FC<RoletaTemasProps> = ({ setSelectedTema, isSpinning, setIsSpinning }) => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });

  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      setMousePosition({ x: e.clientX, y: e.clientY });
    };

    window.addEventListener('mousemove', handleMouseMove);
    return () => window.removeEventListener('mousemove', handleMouseMove);
  }, []);

  const girarRoleta = () => {
    if (isSpinning) return;
    
    setIsSpinning(true);
    
    const spins = 20 + Math.floor(Math.random() * 20);
    let currentSpin = 0;
    
    const interval = setInterval(() => {
      setCurrentIndex((prev) => (prev + 1) % temas.length);
      currentSpin++;
      
      if (currentSpin >= spins) {
        clearInterval(interval);
        setIsSpinning(false);
        const finalIndex = Math.floor(Math.random() * temas.length);
        setCurrentIndex(finalIndex);
        setSelectedTema(temas[finalIndex]);
      }
    }, 100);
  };

  return (
    <div className="relative">
      {/* Mouse follower effect */}
      <motion.div
        className="fixed w-64 h-64 rounded-full bg-orange-500/10 blur-3xl pointer-events-none z-0"
        style={{
          left: mousePosition.x - 128,
          top: mousePosition.y - 128
        }}
        animate={{
          scale: [1, 1.2, 1],
          opacity: [0.1, 0.3, 0.1]
        }}
        transition={{
          duration: 2,
          repeat: Infinity,
          ease: "easeInOut"
        }}
      />

      <GlassCard
        variant="holographic"
        size="xl"
        glow={true}
        floating={true}
        className="text-center relative overflow-hidden"
      >
        {/* Animated background particles */}
        <div className="absolute inset-0 pointer-events-none">
          {[...Array(15)].map((_, i) => (
            <motion.div
              key={i}
              className="absolute w-1 h-1 bg-orange-400/40 rounded-full"
              style={{
                left: `${Math.random() * 100}%`,
                top: `${Math.random() * 100}%`
              }}
              animate={{
                y: [-10, 10, -10],
                x: [-5, 5, -5],
                opacity: [0.4, 0.8, 0.4],
                scale: [0.5, 1, 0.5]
              }}
              transition={{
                duration: 2 + i * 0.1,
                repeat: Infinity,
                ease: "easeInOut"
              }}
            />
          ))}
        </div>

        <div className="relative z-10">
          <GlowingEffect 
            color="orange" 
            size="lg" 
            className="mb-6 inline-block"
            pulsing={true}
            floating={true}
          >
            <span className="text-4xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-orange-400 to-purple-400">
              Roleta de Temas
            </span>
          </GlowingEffect>

          {/* Roleta Display */}
          <motion.div
            className="relative mb-8"
            animate={isSpinning ? { rotateY: 360 } : {}}
            transition={isSpinning ? { 
              duration: 0.1, 
              repeat: Infinity, 
              ease: "linear" 
            } : {}}
            style={{ transformStyle: 'preserve-3d' }}
          >
            <GlassCard
              variant="glass"
              size="lg"
              glow={isSpinning}
              floating={true}
              className="min-h-[120px] flex items-center justify-center relative"
            >
              <motion.div
                key={currentIndex}
                initial={{ opacity: 0, scale: 0.8, rotateX: -30 }}
                animate={{ opacity: 1, scale: 1, rotateX: 0 }}
                exit={{ opacity: 0, scale: 0.8, rotateX: 30 }}
                transition={{ duration: 0.3 }}
                className="text-center"
              >
                <span className="text-2xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-cosmic-400 to-ice-400">
                  {temas[currentIndex]}
                </span>
              </motion.div>

              {/* Spinning effect overlay */}
              {isSpinning && (
                <motion.div
                  className="absolute inset-0 bg-gradient-to-r from-orange-500/20 to-purple-500/20 rounded-lg"
                  animate={{
                    opacity: [0.2, 0.6, 0.2],
                    scale: [1, 1.05, 1]
                  }}
                  transition={{
                    duration: 0.5,
                    repeat: Infinity,
                    ease: "easeInOut"
                  }}
                />
              )}
            </GlassCard>
          </motion.div>

          {/* Spin Button */}
          <motion.div
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            <GradientButton
              variant="fire"
              size="xl"
              onClick={girarRoleta}
              disabled={isSpinning}
              className="min-w-[200px] relative"
            >
              <motion.div
                animate={isSpinning ? { rotate: 360 } : {}}
                transition={isSpinning ? { 
                  duration: 1, 
                  repeat: Infinity, 
                  ease: "linear" 
                } : {}}
              >
                {isSpinning ? (
                  <Shuffle className="w-6 h-6" />
                ) : (
                  <Play className="w-6 h-6" />
                )}
              </motion.div>
              {isSpinning ? 'Girando...' : 'Girar Roleta'}
            </GradientButton>
          </motion.div>

          {/* Instructions */}
          <motion.div
            className="mt-6"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.5 }}
          >
            <p className="text-gray-300 text-sm">
              Clique no botão para descobrir um tema incrível para sua conversa!
            </p>
          </motion.div>
        </div>
      </GlassCard>
    </div>
  );
};

const RoletaPage: React.FC<RoletaPageProps> = ({ onBack }) => {
  const [selectedTema, setSelectedTema] = useState<string>('');
  const [isSpinning, setIsSpinning] = useState(false);
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });
  const [isLoaded, setIsLoaded] = useState(false);

  useEffect(() => {
    setIsLoaded(true);
    
    const handleMouseMove = (e: MouseEvent) => {
      setMousePosition({ x: e.clientX, y: e.clientY });
    };

    window.addEventListener('mousemove', handleMouseMove);
    return () => window.removeEventListener('mousemove', handleMouseMove);
  }, []);

  const resetRoleta = () => {
    setSelectedTema('');
    setIsSpinning(false);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-black to-gray-800 relative overflow-hidden">
      {/* Animated background with 4D effects */}
      <div className="absolute inset-0 overflow-hidden">
        <FluidBlob 
          size="xl" 
          color="gradient" 
          animated={true}
          className="absolute top-10 left-10 opacity-20" 
        />
        <FluidBlob 
          size="lg" 
          color="orange" 
          animated={true}
          className="absolute top-1/3 right-20 opacity-15" 
        />
        <FluidBlob 
          size="md" 
          color="purple" 
          animated={true}
          className="absolute bottom-20 left-1/4 opacity-25" 
        />
        
        {/* Floating particles */}
        <div className="absolute inset-0 pointer-events-none">
          {[...Array(20)].map((_, i) => (
            <motion.div
              key={i}
              className="absolute w-1 h-1 bg-orange-400/30 rounded-full"
              style={{
                left: `${Math.random() * 100}%`,
                top: `${Math.random() * 100}%`
              }}
              animate={{
                y: [-20, 20, -20],
                x: [-10, 10, -10],
                opacity: [0.3, 0.8, 0.3],
                scale: [0.5, 1.5, 0.5]
              }}
              transition={{
                duration: 4 + i * 0.2,
                repeat: Infinity,
                ease: "easeInOut"
              }}
            />
          ))}
        </div>
      </div>

      {/* Mouse follower effect */}
      <motion.div
        className="fixed w-96 h-96 rounded-full bg-orange-500/10 blur-3xl pointer-events-none z-0"
        style={{
          left: mousePosition.x - 192,
          top: mousePosition.y - 192
        }}
        animate={{
          scale: [1, 1.2, 1],
          opacity: [0.1, 0.3, 0.1]
        }}
        transition={{
          duration: 3,
          repeat: Infinity,
          ease: "easeInOut"
        }}
      />

      <div className="relative z-10 container mx-auto px-6 py-12">
        {/* Header */}
        <motion.header
          className="flex justify-between items-center mb-16"
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: isLoaded ? 1 : 0, y: isLoaded ? 0 : -20 }}
          transition={{ duration: 0.8 }}
        >
          <GlowingEffect color="orange" size="sm" className="flex items-center gap-3">
            <Flame className="w-8 h-8 text-orange-400" />
            <span className="text-4xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-fire-400 to-nature-400">
              PapoFire
            </span>
          </GlowingEffect>

          <div className="flex gap-4">
            <LiquidButton 
              variant="secondary" 
              size="lg"
              onClick={onBack}
            >
              <RotateCcw className="w-5 h-5" />
              Voltar
            </LiquidButton>
          </div>
        </motion.header>

        {/* Main Content */}
        <motion.div
          className="max-w-2xl mx-auto"
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: isLoaded ? 1 : 0, scale: isLoaded ? 1 : 0.9 }}
          transition={{ duration: 1, delay: 0.2 }}
        >
          <RoletaTemas 
            setSelectedTema={setSelectedTema}
            isSpinning={isSpinning}
            setIsSpinning={setIsSpinning}
          />

          {/* Result Display */}
          {selectedTema && !isSpinning && (
            <motion.div
              className="mt-8"
              initial={{ opacity: 0, y: 30, scale: 0.8 }}
              animate={{ opacity: 1, y: 0, scale: 1 }}
              transition={{ duration: 0.8, delay: 0.3 }}
            >
              <GlassCard
                variant="holographic"
                size="xl"
                glow={true}
                floating={true}
                className="text-center"
              >
                <GlowingEffect 
                  color="green" 
                  size="lg" 
                  className="mb-4 inline-block"
                  pulsing={true}
                >
                  <Star className="w-8 h-8" />
                </GlowingEffect>

                <span className="text-4xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-nature-400 to-ice-400">
                  Tema Selecionado!
                </span>

                <motion.div
                  className="mb-6"
                  whileHover={{ scale: 1.05, rotateY: 5 }}
                >
                  <GlassCard
                    variant="glass"
                    size="lg"
                    glow={true}
                    className="bg-gradient-to-r from-green-500/20 to-blue-500/20"
                  >
                    <span className="text-2xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-ice-400 to-cosmic-400">
                      {selectedTema}
                    </span>
                  </GlassCard>
                </motion.div>

                <p className="text-gray-300 mb-6">
                  Agora você tem um tema incrível para iniciar uma conversa envolvente!
                </p>

                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                  <GradientButton
                    variant="nature"
                    size="lg"
                    onClick={resetRoleta}
                  >
                    <Shuffle className="w-5 h-5" />
                    Girar Novamente
                  </GradientButton>

                  <LiquidButton 
                    variant="fire" 
                    size="lg"
                    onClick={() => {
                      navigator.clipboard.writeText(selectedTema);
                      // Aqui você pode adicionar uma notificação de sucesso
                    }}
                  >
                    <Heart className="w-5 h-5" />
                    Copiar Tema
                  </LiquidButton>
                </div>
              </GlassCard>
            </motion.div>
          )}

          {/* Tips Section */}
          <motion.div
            className="mt-12"
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.5 }}
          >
            <GlassCard
              variant="glass"
              size="lg"
              glow={false}
              floating={true}
              className="text-center"
            >
              <GlowingEffect 
                color="purple" 
                size="sm" 
                className="mb-4 inline-block"
              >
                <Crown className="w-6 h-6" />
              </GlowingEffect>

              <span className="text-4xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-cosmic-400 to-fire-400">
                Dicas para Usar o Tema
              </span>

              <div className="text-left space-y-3 text-gray-300">
                <p>• Use o tema como ponto de partida para a conversa</p>
                <p>• Faça perguntas abertas sobre o assunto</p>
                <p>• Compartilhe suas próprias experiências relacionadas</p>
                <p>• Seja genuíno e demonstre interesse real</p>
              </div>
            </GlassCard>
          </motion.div>
        </motion.div>
      </div>
    </div>
  );
};

export default RoletaPage;
export { RoletaPage };
