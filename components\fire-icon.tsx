import React from 'react';
import { motion } from 'framer-motion';

interface FireIconProps {
  size?: number;
  className?: string;
  animated?: boolean;
  color?: 'default' | 'blue' | 'purple' | 'green';
}

export const FireIcon: React.FC<FireIconProps> = ({ 
  size = 24, 
  className = '', 
  animated = true,
  color = 'default'
}) => {
  const colorMap = {
    default: {
      primary: '#f59e0b',
      secondary: '#ef4444',
      tertiary: '#fbbf24'
    },
    blue: {
      primary: '#3b82f6',
      secondary: '#1d4ed8',
      tertiary: '#60a5fa'
    },
    purple: {
      primary: '#8b5cf6',
      secondary: '#7c3aed',
      tertiary: '#a78bfa'
    },
    green: {
      primary: '#10b981',
      secondary: '#059669',
      tertiary: '#34d399'
    }
  };

  const colors = colorMap[color];

  const flameVariants = {
    initial: { scale: 1, rotate: 0 },
    animate: {
      scale: [1, 1.1, 1],
      rotate: [-2, 2, -2],
      transition: {
        duration: 2,
        repeat: Infinity,
        ease: "easeInOut"
      }
    }
  };

  const innerFlameVariants = {
    initial: { scale: 1, y: 0 },
    animate: {
      scale: [1, 1.05, 1],
      y: [-1, 1, -1],
      transition: {
        duration: 1.5,
        repeat: Infinity,
        ease: "easeInOut"
      }
    }
  };

  return (
    <motion.div
      className={`inline-block ${className}`}
      variants={animated ? flameVariants : {}}
      initial="initial"
      animate={animated ? "animate" : "initial"}
    >
      <svg
        width={size}
        height={size}
        viewBox="0 0 24 24"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <defs>
          <radialGradient id={`fireGradient-${color}`} cx="50%" cy="50%" r="50%">
            <stop offset="0%" stopColor={colors.tertiary} />
            <stop offset="50%" stopColor={colors.primary} />
            <stop offset="100%" stopColor={colors.secondary} />
          </radialGradient>
          <filter id={`glow-${color}`}>
            <feGaussianBlur stdDeviation="2" result="coloredBlur"/>
            <feMerge> 
              <feMergeNode in="coloredBlur"/>
              <feMergeNode in="SourceGraphic"/>
            </feMerge>
          </filter>
        </defs>
        
        {/* Outer flame */}
        <motion.path
          d="M12 2C8 2 5 5 5 9c0 2 1 4 2 5.5C8 16 9 17 10 18c1 1 2 2 2 3 0-1 1-2 2-3 1-1 2-2 3-3.5C18 13 19 11 19 9c0-4-3-7-7-7z"
          fill={`url(#fireGradient-${color})`}
          filter={`url(#glow-${color})`}
          variants={animated ? flameVariants : {}}
          initial="initial"
          animate={animated ? "animate" : "initial"}
        />
        
        {/* Inner flame */}
        <motion.path
          d="M12 6c-2 0-3.5 1.5-3.5 3.5 0 1 0.5 2 1 2.5 0.5 0.5 1 1 1.5 1.5 0.5 0.5 1 1 1 1.5 0-0.5 0.5-1 1-1.5 0.5-0.5 1-1 1.5-1.5 0.5-0.5 1-1.5 1-2.5C15.5 7.5 14 6 12 6z"
          fill={colors.tertiary}
          opacity="0.8"
          variants={animated ? innerFlameVariants : {}}
          initial="initial"
          animate={animated ? "animate" : "initial"}
        />
        
        {/* Core */}
        <motion.circle
          cx="12"
          cy="12"
          r="2"
          fill={colors.tertiary}
          opacity="0.6"
          variants={animated ? {
            initial: { scale: 1 },
            animate: {
              scale: [1, 1.2, 1],
              transition: {
                duration: 1,
                repeat: Infinity,
                ease: "easeInOut"
              }
            }
          } : {}}
          initial="initial"
          animate={animated ? "animate" : "initial"}
        />
      </svg>
    </motion.div>
  );
};