import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Input } from '../../components/ui/input';
import { Button } from '../../components/ui/button';
import { Eye, EyeOff } from 'lucide-react';
import { PapoFireLogo } from '../../components/ui/icons';

const LoginPage: React.FC = () => {
  const [isSignUp, setIsSignUp] = useState(false);
  const [showPassword, setShowPassword] = useState(false);

  return (
    <div className="min-h-screen bg-black flex items-center justify-center p-4">
      <motion.div 
        className="w-full max-w-sm bg-gray-900/50 border border-gray-800 rounded-2xl p-8"
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ duration: 0.5 }}
      >
        <div className="text-center mb-8">
          <PapoFireLogo />
          <h2 className="text-3xl font-bold text-white mt-4">{isSignUp ? 'Cadastrar' : 'Entrar'}</h2>
        </div>

        <div className="flex justify-center mb-6">
          <div className="bg-gray-800 p-1 rounded-full flex gap-1">
            <Button
              onClick={() => setIsSignUp(false)}
              className={`px-6 py-2 rounded-full text-sm font-semibold transition-colors ${!isSignUp ? 'bg-orange-600 text-white' : 'bg-transparent text-gray-400 hover:text-white'}`}
            >
              Entrar
            </Button>
            <Button
              onClick={() => setIsSignUp(true)}
              className={`px-6 py-2 rounded-full text-sm font-semibold transition-colors ${isSignUp ? 'bg-gray-700 text-white' : 'bg-transparent text-gray-400 hover:text-white'}`}
            >
              Cadastrar
            </Button>
          </div>
        </div>

        <form className="space-y-6">
          {isSignUp && (
            <div className="relative">
              <Input
                type="text"
                placeholder="Nome"
                className="h-12 w-full bg-gray-800 border-gray-700 rounded-lg px-4 text-white focus:ring-2 focus:ring-orange-500"
              />
            </div>
          )}
          <div className="relative">
            <Input
              type="email"
              placeholder="Email"
              className="h-12 w-full bg-gray-800 border-gray-700 rounded-lg px-4 text-white focus:ring-2 focus:ring-orange-500"
            />
          </div>

          <div className="relative">
            <Input
              type={showPassword ? 'text' : 'password'}
              placeholder="Senha"
              className="h-12 w-full bg-gray-800 border-gray-700 rounded-lg px-4 text-white focus:ring-2 focus:ring-orange-500"
            />
            <button
              type="button"
              onClick={() => setShowPassword(!showPassword)}
              className="absolute inset-y-0 right-0 flex items-center px-4 text-gray-400"
            >
              {showPassword ? <EyeOff size={20} /> : <Eye size={20} />}
            </button>
          </div>

          {isSignUp && (
            <div className="relative">
              <Input
                type={showPassword ? 'text' : 'password'}
                placeholder="Confirmar Senha"
                className="h-12 w-full bg-gray-800 border-gray-700 rounded-lg px-4 text-white focus:ring-2 focus:ring-orange-500"
              />
            </div>
          )}

          <Button
            type="submit"
            className="w-full h-12 font-bold rounded-lg bg-orange-600 hover:bg-orange-700 text-white transition-colors"
          >
            {isSignUp ? 'Cadastrar' : 'Entrar'}
          </Button>
        </form>
      </motion.div>
    </div>
  );
};

export default LoginPage;