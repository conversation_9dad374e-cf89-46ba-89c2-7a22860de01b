import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { GlassCard } from '../../components/glass-card';
import { Button } from '../../components/ui/button';
import { ChevronLeft, Upload, FileText, Loader2, Co<PERSON>, Check, Flame, MessageCircle, Heart, Star } from 'lucide-react';

const tiposAnalise = [
  { icon: <Heart className="w-5 h-5 text-pink-400" />, label: 'Respostas Sólidas', desc: 'Sugestões para manter a conversa fluindo.' },
  { icon: <MessageCircle className="w-5 h-5 text-orange-400" />, label: 'Conversa Quebrada', desc: 'Como retomar um papo parado.' },
  { icon: <Star className="w-5 h-5 text-yellow-400" />, label: 'Relacionamento', desc: 'Dicas para criar conexão.' },
  { icon: <Flame className="w-5 h-5 text-red-500" />, label: 'Conversa Tímida', desc: 'Como engajar pessoas mais reservadas.' },
];

const ChatPage = ({
  onBack = () => {},
}) => {
  const [uploadedFile, setUploadedFile] = useState<string | null>(null);
  const [analysis, setAnalysis] = useState<string>('');
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [copied, setCopied] = useState(false);
  const [openAccordion, setOpenAccordion] = useState<number | null>(null);

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        setUploadedFile(e.target?.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  const analyzeChat = async () => {
    if (!uploadedFile) return;
    setIsAnalyzing(true);
    await new Promise(resolve => setTimeout(resolve, 2500));
    setAnalysis('Análise premium da conversa: \n\n- O papo está fluindo bem!\n- Sugestão: faça uma pergunta aberta sobre o último assunto.\n- Dica: use emojis para deixar a conversa mais leve.');
    setIsAnalyzing(false);
  };

  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (err) {
      console.error('Erro ao copiar:', err);
    }
  };

  return (
    <motion.div 
      className="min-h-screen p-4 bg-gradient-to-br from-green-900 via-black to-emerald-900 text-white relative overflow-hidden"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.5 }}
    >
      <div className="relative z-10 max-w-lg mx-auto">
        {/* Header */}
        <div className="flex items-center mb-8 pt-8">
          <motion.button 
            onClick={onBack}
            className="mr-4 p-3 rounded-full hover:bg-emerald-800/50 transition-all cursor-hover"
            whileHover={{ scale: 1.1, rotate: -10 }}
            whileTap={{ scale: 0.9 }}
          >
            <ChevronLeft className="w-6 h-6" />
          </motion.button>
          <div>
            <h1 className="text-2xl font-bold">Análise de Conversa 💬</h1>
            <p className="text-sm text-emerald-200">A resposta perfeita para qualquer chat</p>
          </div>
        </div>

        <div className="space-y-6">
          {/* Upload Section */}
          <GlassCard>
            <div className="space-y-4">
              <h3 className="font-bold text-lg flex items-center gap-2">
                <Upload className="w-5 h-5" />
                Envie o Print da Conversa
              </h3>
              <div className="border-2 border-dashed border-emerald-600 rounded-xl p-8 text-center">
                <input
                  type="file"
                  accept="image/*"
                  onChange={handleFileUpload}
                  className="hidden"
                  id="chat-upload"
                />
                <label htmlFor="chat-upload" className="cursor-pointer">
                  {uploadedFile ? (
                    <img 
                      src={uploadedFile} 
                      alt="Conversa" 
                      className="max-w-full h-48 object-contain mx-auto rounded-lg"
                    />
                  ) : (
                    <div className="space-y-3">
                      <Upload className="w-12 h-12 mx-auto text-emerald-400" />
                      <p className="text-emerald-200">Clique para fazer upload do print da conversa</p>
                    </div>
                  )}
                </label>
              </div>
            </div>
          </GlassCard>

          {/* Analyze Button */}
          {uploadedFile && (
            <Button
              onClick={analyzeChat}
              disabled={isAnalyzing}
              className="w-full py-4 bg-gradient-to-r from-green-500 to-emerald-500 text-white font-bold rounded-2xl shadow-lg hover:scale-105 transition-transform"
            >
              {isAnalyzing ? (
                <span className="flex items-center gap-2">
                  <Loader2 className="w-5 h-5 animate-spin" />
                  Analisando...
                </span>
              ) : (
                <span className="flex items-center gap-2">
                  <FileText className="w-5 h-5" />
                  Analisar Conversa
                </span>
              )}
            </Button>
          )}

          {/* Analysis Result */}
          {analysis && (
            <GlassCard>
              <div className="space-y-4">
                <h3 className="font-bold text-lg flex items-center gap-2">
                  <FileText className="w-5 h-5" />
                  Análise da IA
                </h3>
                <p className="text-emerald-100 whitespace-pre-wrap">{analysis}</p>
                <Button
                  onClick={() => copyToClipboard(analysis)}
                  className="w-full py-3 bg-gradient-to-r from-green-500 to-emerald-500 text-white font-bold rounded-2xl shadow-lg hover:scale-105 transition-transform"
                >
                  {copied ? (
                    <span className="flex items-center gap-2">
                      <Check className="w-5 h-5" />
                      Copiado!
                    </span>
                  ) : (
                    <span className="flex items-center gap-2">
                      <Copy className="w-5 h-5" />
                      Copiar Análise
                    </span>
                  )}
                </Button>
              </div>
            </GlassCard>
          )}

          {/* Accordion de tipos de análise */}
          <div className="mt-10">
            <h2 className="text-xl font-bold mb-4 text-emerald-200">Tipos de Análise</h2>
            <div className="space-y-3">
              {tiposAnalise.map((tipo, idx) => (
                <GlassCard
                  key={idx}
                  variant="glass"
                  size="md"
                  glow={false}
                  floating={true}
                  className="cursor-pointer"
                  onClick={() => setOpenAccordion(openAccordion === idx ? null : idx)}
                >
                  <div className="flex items-center gap-3">
                    {tipo.icon}
                    <span className="font-semibold text-lg text-emerald-100">{tipo.label}</span>
                  </div>
                  {openAccordion === idx && (
                    <motion.div
                      initial={{ opacity: 0, y: -10 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.3 }}
                      className="mt-2 text-emerald-200 text-sm"
                    >
                      {tipo.desc}
                    </motion.div>
                  )}
                </GlassCard>
              ))}
            </div>
          </div>
        </div>
      </div>
    </motion.div>
  );
};

export default ChatPage; 