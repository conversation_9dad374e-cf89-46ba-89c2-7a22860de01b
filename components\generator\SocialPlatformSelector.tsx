import React from 'react';
import type { SocialPlatformId } from '../../types';
import { SOCIAL_PLATFORMS } from '../../constants';

interface SocialPlatformSelectorProps {
  selected: SocialPlatformId;
  onChange: (platform: SocialPlatformId) => void;
}

export const SocialPlatformSelector: React.FC<SocialPlatformSelectorProps> = ({ selected, onChange }) => {
  return (
    <div className="flex border-b border-white/10 mb-4 overflow-x-auto">
      {SOCIAL_PLATFORMS.map(platform => (
        <button 
          key={platform.id} 
          onClick={() => onChange(platform.id)} 
          className={`px-4 py-2 text-sm font-medium transition-colors whitespace-nowrap ${
            selected === platform.id 
              ? 'text-orange-400 border-b-2 border-orange-400' 
              : 'text-gray-400 hover:text-white'
          }`}
        >
          {platform.label}
        </button>
      ))}
    </div>
  );
};
