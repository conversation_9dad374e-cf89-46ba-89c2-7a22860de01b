import React, { useState, useRef, useEffect } from 'react';
import { motion } from 'framer-motion';

interface GradientButtonProps {
  children: React.ReactNode;
  onClick?: () => void;
  variant?: 'fire' | 'ocean' | 'sunset' | 'forest' | 'cosmic' | 'aurora';
  size?: 'sm' | 'md' | 'lg' | 'xl';
  disabled?: boolean;
  className?: string;
  animated?: boolean;
  glow?: boolean;
}

export const GradientButton: React.FC<GradientButtonProps> = ({
  children,
  onClick,
  variant = 'fire',
  size = 'md',
  disabled = false,
  className = '',
  animated = true,
  glow = true
}) => {
  const [isHovered, setIsHovered] = useState(false);
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });
  const buttonRef = useRef<HTMLButtonElement>(null);

  const variants = {
    fire: {
      gradient: 'from-orange-400 via-red-500 to-yellow-600',
      shadow: 'shadow-orange-500/50',
      glow: 'drop-shadow-[0_0_20px_rgba(251,146,60,0.5)]'
    },
    ocean: {
      gradient: 'from-blue-400 via-cyan-500 to-teal-600',
      shadow: 'shadow-blue-500/50',
      glow: 'drop-shadow-[0_0_20px_rgba(59,130,246,0.5)]'
    },
    sunset: {
      gradient: 'from-pink-400 via-purple-500 to-indigo-600',
      shadow: 'shadow-purple-500/50',
      glow: 'drop-shadow-[0_0_20px_rgba(168,85,247,0.5)]'
    },
    forest: {
      gradient: 'from-green-400 via-emerald-500 to-teal-600',
      shadow: 'shadow-green-500/50',
      glow: 'drop-shadow-[0_0_20px_rgba(34,197,94,0.5)]'
    },
    cosmic: {
      gradient: 'from-purple-400 via-pink-500 to-red-500',
      shadow: 'shadow-pink-500/50',
      glow: 'drop-shadow-[0_0_20px_rgba(236,72,153,0.5)]'
    },
    aurora: {
      gradient: 'from-cyan-400 via-purple-500 to-pink-500',
      shadow: 'shadow-cyan-500/50',
      glow: 'drop-shadow-[0_0_20px_rgba(34,211,238,0.5)]'
    }
  };

  const selectedVariant = variants[variant] || variants.fire;

  const sizes = {
    sm: 'px-4 py-2 text-sm',
    md: 'px-6 py-3 text-base',
    lg: 'px-8 py-4 text-lg',
    xl: 'px-10 py-5 text-xl'
  };

  useEffect(() => {
    const button = buttonRef.current;
    if (!button) return;

    const handleMouseMove = (e: MouseEvent) => {
      const rect = button.getBoundingClientRect();
      const x = ((e.clientX - rect.left) / rect.width) * 100;
      const y = ((e.clientY - rect.top) / rect.height) * 100;
      setMousePosition({ x, y });
    };

    button.addEventListener('mousemove', handleMouseMove);
    return () => button.removeEventListener('mousemove', handleMouseMove);
  }, []);

  return (
    <motion.button
      ref={buttonRef}
      className={`
        relative overflow-hidden
        ${sizes[size]}
        bg-gradient-to-r ${selectedVariant.gradient}
        rounded-2xl
        font-semibold
        text-white
        border-0
        cursor-pointer
        transition-all duration-300
        transform-gpu
        ${disabled ? 'opacity-50 cursor-not-allowed' : ''}
        ${glow ? `${selectedVariant.shadow} shadow-lg` : ''}
        ${animated ? 'hover:scale-105' : ''}
        ${className}
      `}
      style={{
        background: animated ? `
          radial-gradient(circle at ${mousePosition.x}% ${mousePosition.y}%, 
            rgba(255, 255, 255, 0.2) 0%, 
            transparent 50%
          ),
          linear-gradient(135deg, 
            var(--tw-gradient-from), 
            var(--tw-gradient-via), 
            var(--tw-gradient-to)
          )
        ` : undefined,
        transformStyle: 'preserve-3d'
      }}
      initial={{ scale: 1 }}
      whileHover={{ 
        scale: disabled ? 1 : (animated ? 1.05 : 1),
        rotateX: disabled ? 0 : 2,
        rotateY: disabled ? 0 : 2
      }}
      whileTap={{ 
        scale: disabled ? 1 : 0.98,
        rotateX: disabled ? 0 : -2,
        rotateY: disabled ? 0 : -2
      }}
      onHoverStart={() => setIsHovered(true)}
      onHoverEnd={() => setIsHovered(false)}
      onClick={onClick}
      disabled={disabled}
    >
      {/* Animated background overlay */}
      <motion.div
        className="absolute inset-0 rounded-2xl"
        style={{
          background: `
            linear-gradient(45deg, 
              transparent 30%, 
              rgba(255, 255, 255, 0.3) 50%, 
              transparent 70%
            )
          `,
          backgroundSize: '200% 200%'
        }}
        animate={{
          backgroundPosition: isHovered && animated ? ['0% 0%', '100% 100%'] : '0% 0%'
        }}
        transition={{
          duration: 1.5,
          repeat: isHovered && animated ? Infinity : 0,
          ease: "linear"
        }}
      />

      {/* Floating particles */}
      {animated && isHovered && (
        <div className="absolute inset-0 pointer-events-none overflow-hidden rounded-2xl">
          {[...Array(8)].map((_, i) => (
            <motion.div
              key={i}
              className="absolute w-1.5 h-1.5 bg-white/60 rounded-full"
              style={{
                left: `${15 + (i * 12)}%`,
                top: `${25 + (i * 8)}%`
              }}
              animate={{
                y: [-8, 8, -8],
                x: [-4, 4, -4],
                opacity: [0.4, 1, 0.4],
                scale: [0.8, 1.2, 0.8]
              }}
              transition={{
                duration: 2.5 + i * 0.4,
                repeat: Infinity,
                ease: "easeInOut"
              }}
            />
          ))}
        </div>
      )}

      {/* Glow effect */}
      {glow && !disabled && (
        <motion.div
          className={`absolute inset-0 rounded-2xl blur-xl ${selectedVariant.glow}`}
          animate={{
            opacity: isHovered ? 0.8 : 0.4
          }}
          transition={{ duration: 0.3 }}
        />
      )}

      {/* Content */}
      <span className="relative z-10 flex items-center justify-center gap-2">
        {children}
      </span>

      {/* Border highlight */}
      <motion.div
        className="absolute inset-0 rounded-2xl border-2 border-white/20"
        animate={{
          borderColor: isHovered && animated ? 'rgba(255, 255, 255, 0.4)' : 'rgba(255, 255, 255, 0.2)'
        }}
        transition={{ duration: 0.3 }}
      />
    </motion.button>
  );
};

export default GradientButton;