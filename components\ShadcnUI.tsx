
import React, { useState, useRef, useEffect, FC, PropsWithChildren } from 'react';
import { motion, useScroll, useTransform, useMotionValue, useSpring, AnimatePresence, useMotionValueEvent } from 'framer-motion';
import { cn } from '../lib/utils';
import { Check, Rocket, MessageCircle, Camera, Brain } from 'lucide-react';

// --- <PERSON>quid Glass Button ---
export const LiquidGlassButton: FC<PropsWithChildren<{ onClick?: () => void; className?: string; disabled?: boolean; }>> = ({ children, onClick, className = '', disabled = false }) => {
    return (
        <motion.button
            onClick={onClick}
            disabled={disabled}
            className={cn(
                "relative overflow-hidden px-8 py-3 rounded-xl font-semibold text-white transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center gap-2 bg-orange-600 hover:bg-orange-700",
                className
            )}
            whileHover={{ scale: disabled ? 1 : 1.05, boxShadow: '0 0 20px rgba(234, 88, 12, 0.5)' }}
            whileTap={{ scale: disabled ? 1 : 0.95 }}
        >
            <span className="relative z-10">{children}</span>
        </motion.button>
    );
};


// --- Vapour Text Effect ---
export const VapourTextEffect: FC<{ text: string; className?: string }> = ({ text, className }) => {
    const letters = text.split("");
    return (
        <div className="flex">
            {letters.map((letter, i) => (
                <motion.span
                    key={`${letter}-${i}`}
                    initial={{ opacity: 0, y: 20, filter: 'blur(5px)' }}
                    animate={{ opacity: 1, y: 0, filter: 'blur(0px)' }}
                    transition={{ delay: i * 0.05, duration: 0.5, ease: 'easeOut' }}
                    className={cn("whitespace-pre", className)}
                >
                    {letter === ' ' ? '\u00A0' : letter}
                </motion.span>
            ))}
        </div>
    );
};


// --- Glass Card (Enhanced) ---
export const GlassCard: FC<PropsWithChildren<{ className?: string, onClick?: () => void }>> = ({ children, className = '', onClick }) => {
    return (
        <motion.div
            onClick={onClick}
            className={cn("relative p-6 rounded-3xl border border-white/10 bg-slate-900/80 backdrop-blur-xl group", className)}
            whileHover={{ 
                y: -5, 
                boxShadow: '0 0 30px rgba(234, 88, 12, 0.4)',
                borderColor: 'rgba(234, 88, 12, 0.5)'
            }}
            transition={{ type: 'spring', stiffness: 300, damping: 20 }}
        >
            <div className="absolute -inset-px rounded-3xl bg-gradient-to-r from-orange-500/50 via-purple-500/50 to-pink-500/50 opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none" />
            <div className="relative z-10">
                {children}
            </div>
        </motion.div>
    );
};


// --- Tubelight Navbar ---
export const TubelightNavbar: FC<{
    navItems: { name: string; link: string; action?: () => void }[];
    activeItem: string;
    onNavItemClick: (link: string, action?: () => void) => void;
    userLoggedIn: boolean;
    onAuthClick: () => void;
    onLogoutClick: () => void;
}> = ({ navItems, activeItem, onNavItemClick, userLoggedIn, onAuthClick, onLogoutClick }) => {
    const [hovered, setHovered] = useState<string | null>(null);

    return (
        <motion.nav 
            initial={{ y: -100 }}
            animate={{ y: 0 }}
            transition={{ duration: 0.5 }}
            className="fixed top-4 left-1/2 -translate-x-1/2 flex items-center justify-between gap-8 p-3 bg-black/50 backdrop-blur-lg rounded-full border border-white/10 z-50"
        >
            <div className="flex items-center gap-2 cursor-pointer" onClick={() => onNavItemClick('landing')}>
                <img src="/assets/fire-logo.png" alt="PapoFire Logo" className="w-8 h-8"/>
                <span className="font-bold text-lg">PapoFire</span>
            </div>
            <div className="hidden md:flex items-center gap-6">
                {navItems.map((item) => (
                    <div
                        key={item.name}
                        className="relative cursor-pointer"
                        onMouseEnter={() => setHovered(item.link)}
                        onMouseLeave={() => setHovered(null)}
                        onClick={() => onNavItemClick(item.link, item.action)}
                    >
                        <span className={cn("transition-colors", activeItem === item.link ? 'text-white' : 'text-gray-400 hover:text-white')}>{item.name}</span>
                        {(activeItem === item.link || hovered === item.link) && (
                            <motion.div
                                className="absolute -bottom-1.5 left-0 w-full h-0.5 bg-gradient-to-r from-orange-500 to-red-500 rounded-full"
                                layoutId="tubelight"
                                initial={{ opacity: 0 }}
                                animate={{ opacity: 1 }}
                                exit={{ opacity: 0 }}
                            />
                        )}
                    </div>
                ))}
            </div>
            <div className="flex items-center">
                {userLoggedIn ? (
                     <LiquidGlassButton onClick={onLogoutClick} className="px-4 py-2 text-sm">Sair</LiquidGlassButton>
                ) : (
                     <LiquidGlassButton onClick={onAuthClick} className="px-4 py-2 text-sm">Entrar</LiquidGlassButton>
                )}
            </div>
        </motion.nav>
    );
};

 

  

// --- Pricing Section ---
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "./ui/accordion";

export const PricingSection: FC<{ onCtaClick: (plan: string) => void; }> = ({ onCtaClick }) => {
    const pricingPlans = [
        {
            name: 'Free',
            price: 'R$0',
            features: [
                { text: '5 Gerações/dia', icon: <MessageCircle className="w-4 h-4" /> },
                { text: 'Modo Básico', icon: <Brain className="w-4 h-4" /> },
            ],
            cta: 'Começar de graça',
            isPopular: false,
            color: 'gray'
        },
        {
            name: 'PRO',
            price: 'R$19',
            features: [
                { text: 'Gerações Ilimitadas', icon: <Rocket className="w-4 h-4" /> },
                { text: 'Todos os Modos de IA', icon: <Brain className="w-4 h-4" /> },
                { text: 'Análise de Imagem', icon: <Camera className="w-4 h-4" /> },
                { text: 'Suporte Prioritário', icon: <Check className="w-4 h-4" /> },
            ],
            cta: 'Virar PRO',
            isPopular: true,
            color: 'green'
        },
        {
            name: 'ULTRA',
            price: 'R$49',
            features: [
                { text: 'Tudo do PRO', icon: <Check className="w-4 h-4" /> },
                { text: 'Análise de Perfil', icon: <Brain className="w-4 h-4" /> },
                { text: 'Tom de Voz Personalizado', icon: <MessageCircle className="w-4 h-4" /> },
                { text: 'Consultoria com IA', icon: <Rocket className="w-4 h-4" /> },
            ],
            cta: 'Virar ULTRA',
            isPopular: false,
            color: 'yellow'
        },
    ];

    // For Accordion on mobile
    const renderAccordion = () => (
        <Accordion type="single" collapsible className="w-full space-y-4 md:hidden">
            {pricingPlans.map((plan) => (
                <AccordionItem value={plan.name} key={plan.name} className="border border-slate-800 rounded-lg bg-slate-900/80 px-4">
                    <AccordionTrigger className="text-white font-bold text-lg hover:no-underline">
                        <div className="flex items-center gap-4">
                            <span>{plan.name}</span>
                            {plan.isPopular && <span className="text-xs bg-brand-green text-white px-2 py-0.5 rounded-full">Mais usado</span>}
                        </div>
                    </AccordionTrigger>
                    <AccordionContent>
                        <div className="p-4 flex flex-col items-center text-center">
                             <p className="text-4xl font-extrabold my-4">
                                {plan.price}<span className="text-base font-medium text-gray-400">/mês</span>
                            </p>
                            <ul className="space-y-3 mb-6 text-left">
                                {plan.features.map(feature => (
                                    <li key={feature.text} className="flex items-center gap-3">
                                        {React.cloneElement(feature.icon, { className: 'w-5 h-5 text-brand-green' })}
                                        <span className="text-gray-300 text-sm">{feature.text}</span>
                                    </li>
                                ))}
                            </ul>
                            <LiquidGlassButton 
                                onClick={() => onCtaClick(plan.name)}
                                className={cn(
                                    'w-full font-bold text-lg py-3',
                                    plan.isPopular ? 'bg-brand-green hover:bg-green-600' : 'bg-slate-700 hover:bg-slate-600'
                                )}
                            >
                                {plan.cta}
                            </LiquidGlassButton>
                        </div>
                    </AccordionContent>
                </AccordionItem>
            ))}
        </Accordion>
    );

    // For Table on desktop
    const renderTable = () => (
        <div className="hidden md:block w-full max-w-5xl bg-slate-900/80 rounded-xl border border-slate-800 overflow-hidden">
            <table className="w-full text-left">
                <thead>
                    <tr className="border-b border-slate-800">
                        <th className="p-6">Funcionalidades</th>
                        {pricingPlans.map(plan => (
                            <th key={plan.name} className="p-6 text-center">
                                <h3 className={`text-xl font-bold ${plan.isPopular ? 'text-brand-green' : 'text-white'}`}>{plan.name}</h3>
                                 {plan.isPopular && <span className="text-xs bg-brand-green text-white px-2 py-0.5 rounded-full">Mais usado</span>}
                            </th>
                        ))}
                    </tr>
                </thead>
                <tbody>
                    {/* Price Row */}
                    <tr className="bg-slate-800/50">
                        <td className="p-6 font-semibold">Preço</td>
                        {pricingPlans.map(plan => (
                             <td key={plan.name} className="p-6 text-center">
                                <p className="text-2xl font-bold">
                                    {plan.price}<span className="text-sm font-normal text-gray-400">/mês</span>
                                </p>
                            </td>
                        ))}
                    </tr>
                    {/* Features Rows with Zebra Stripes */}
                    {[...new Set(pricingPlans.flatMap(p => p.features.map(f => f.text)))].map((featureText, index) => (
                         <tr key={featureText} className={index % 2 === 0 ? 'bg-slate-900' : 'bg-slate-800/50'}>
                             <td className="p-4 pl-6 flex items-center gap-3 text-gray-300">
                                <Check className="w-4 h-4 text-gray-500" /> {featureText}
                             </td>
                             {pricingPlans.map(plan => (
                                 <td key={plan.name} className="p-4 text-center">
                                     {plan.features.some(f => f.text === featureText) ? (
                                         <Check className="w-6 h-6 text-brand-green mx-auto" />
                                     ) : (
                                        <span className="text-gray-500">-</span>
                                     )}
                                 </td>
                             ))}
                         </tr>
                    ))}
                     {/* CTA Row */}
                     <tr className="bg-slate-800/50">
                         <td className="p-6"></td>
                         {pricingPlans.map(plan => (
                             <td key={plan.name} className="p-6 text-center">
                                 <LiquidGlassButton 
                                    onClick={() => onCtaClick(plan.name)}
                                    className={cn(
                                        'w-full font-bold',
                                        plan.isPopular ? 'bg-brand-green hover:bg-green-600' : 'bg-slate-700 hover:bg-slate-600'
                                    )}
                                >
                                    {plan.cta}
                                </LiquidGlassButton>
                             </td>
                         ))}
                     </tr>
                </tbody>
            </table>
        </div>
    );

    return (
        <>
            {renderAccordion()}
            {renderTable()}
        </>
    );
};