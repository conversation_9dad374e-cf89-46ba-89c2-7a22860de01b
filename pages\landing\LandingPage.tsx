
import React from 'react';
import { motion } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { TubelightNavbar, PricingSection } from '@/components/ShadcnUI';
import DisplayCards from '@/components/ui/display-cards';
import { Flame } from 'lucide-react';

interface LandingPageProps {
  onGetStarted: () => void;
  onLoginClick: () => void;
}

export const LandingPage: React.FC<LandingPageProps> = ({ onGetStarted, onLoginClick }) => {
  const navItems = [
    { name: 'In<PERSON>cio', link: '#hero' },
    { name: 'Funcionalidades', link: '#features' },
    { name: 'Preços', link: '#pricing' },
  ];
  const [isMobile, setIsMobile] = React.useState(false);

  React.useEffect(() => {
    const checkMobile = () => setIsMobile(window.innerWidth < 768);
    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  return (
    <div className="min-h-screen w-full bg-gradient-to-br from-[#0f0c29] via-[#302b63] to-[#24243e] text-white relative overflow-x-hidden">
      {/* Glow Hero Effect */}
      <div className="absolute top-0 left-1/2 -translate-x-1/2 z-0 pointer-events-none">
        <motion.div
          initial={{ opacity: 0, scale: 0.7 }}
          animate={{ opacity: 0.7, scale: 1 }}
          transition={{ duration: 1.2, type: 'spring' }}
          className="w-[480px] h-[480px] bg-gradient-radial from-orange-500/60 via-orange-400/30 to-transparent rounded-full blur-3xl opacity-80"
        />
      </div>
      <TubelightNavbar
        navItems={navItems}
        activeItem={'#hero'}
        onNavItemClick={(link) => {
          const section = document.querySelector(link);
          if (section) {
            section.scrollIntoView({ behavior: 'smooth' });
          }
        }}
        userLoggedIn={false}
        onAuthClick={onLoginClick}
        onLogoutClick={() => {}}
      />
      <main className="w-full max-w-7xl mx-auto flex flex-col items-center p-4 sm:p-8 z-10 relative">
        {/* Hero Section */}
        <motion.section
          id="hero"
          initial={{ opacity: 0, y: -30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, ease: 'easeOut' }}
          className="w-full flex flex-col items-center justify-center text-center py-24 sm:py-32"
        >
          <motion.div
            initial={{ opacity: 0, scale: 0.7 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 1, delay: 0.2, type: 'spring' }}
            className="mb-6"
          >
            <div className="flex flex-col items-center gap-2">
              <span className="inline-flex items-center justify-center w-28 h-28 rounded-full bg-gradient-to-br from-orange-500 via-orange-400 to-yellow-400 shadow-2xl animate-pulse">
                <Flame className="w-20 h-20 text-white drop-shadow-xl" />
              </span>
              <span className="text-xs font-semibold bg-black/40 px-3 py-1 rounded-full mt-2 text-orange-200 tracking-widest uppercase shadow">Powered by AI 🔥</span>
            </div>
          </motion.div>
          <motion.h1
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 1, delay: 0.3, type: 'spring' }}
            className="text-6xl md:text-7xl font-extrabold bg-gradient-to-r from-orange-400 via-pink-500 to-yellow-400 bg-clip-text text-transparent drop-shadow-lg mb-4"
          >
            PapoFire
          </motion.h1>
          <motion.p
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 1, delay: 0.5, type: 'spring' }}
            className="text-lg sm:text-xl font-light mt-2 max-w-xl mx-auto text-orange-100"
          >
            Gerador multi-plataforma com IA brasileira ultra-humanizada <span className="inline-block animate-bounce">🔥</span>
          </motion.p>
          <div className="flex flex-wrap gap-2 justify-center mt-4">
            <span className="bg-white/10 px-3 py-1 rounded-full text-xs text-orange-200">WhatsApp</span>
            <span className="bg-white/10 px-3 py-1 rounded-full text-xs text-pink-200">Instagram</span>
            <span className="bg-white/10 px-3 py-1 rounded-full text-xs text-blue-200">TikTok</span>
            <span className="bg-white/10 px-3 py-1 rounded-full text-xs text-blue-300">Twitter</span>
          </div>
          <motion.div
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.5, delay: 0.7, type: 'spring', stiffness: 150 }}
            className="mt-10"
          >
            <Button
              onClick={onGetStarted}
              className="px-10 py-4 text-lg font-bold bg-gradient-to-r from-orange-500 via-pink-500 to-yellow-400 hover:from-orange-600 hover:to-yellow-500 shadow-lg shadow-orange-500/20 border-0 rounded-2xl transition-all duration-300"
            >
              Comece Grátis
            </Button>
            <Button
              onClick={onLoginClick}
              variant="outline"
              className="ml-4 px-10 py-4 text-lg font-bold border-orange-400 text-orange-200 hover:bg-orange-500/10 rounded-2xl transition-all duration-300"
            >
              Já tenho conta
            </Button>
          </motion.div>
        </motion.section>

        {/* Social Proof Bar */}
                <motion.section id="features" className="w-full py-24">
          <h2 className="text-3xl md:text-4xl font-bold text-center mb-12 text-orange-100 drop-shadow">Funcionalidades Incríveis</h2>
          <DisplayCards />
        </motion.section>

        <motion.section id="social-proof" className="w-full py-12">
          <div className="flex justify-center gap-8 md:gap-16">
            <div className="text-center">
              <h3 className="text-3xl font-bold text-orange-200">10K+</h3>
              <p className="text-orange-100">conversas/mês</p>
            </div>
            <div className="text-center">
              <h3 className="text-3xl font-bold text-orange-200">95%</h3>
              <p className="text-orange-100">de acerto</p>
            </div>
            <div className="text-center">
              <h3 className="text-3xl font-bold text-orange-200">24/7</h3>
              <p className="text-orange-100">online</p>
            </div>
          </div>
        </motion.section>
      
        {/* Plan Comparison Table */}
        <motion.section id="pricing" className="w-full py-24">
          <PricingSection onCtaClick={onGetStarted} />
        </motion.section>
    </main>
  </div>
  );
};
