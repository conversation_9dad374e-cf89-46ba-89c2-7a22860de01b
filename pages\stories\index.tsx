import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { ChevronLeft, Upload, FileText, Loader2, Copy, Check } from 'lucide-react';
import { GlassCard } from '../../components/glass-card';
import { GlowingButton } from '../../components/ui/glowing-effect';

const StoryAnalysisPage: React.FC<{ onBack: () => void }> = ({ onBack }) => {
  const [uploadedImage, setUploadedImage] = useState<string | null>(null);
  const [analysis, setAnalysis] = useState<string>('');
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [copied, setCopied] = useState(false);

  const handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        setUploadedImage(e.target?.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  const analyzeStory = async () => {
    if (!uploadedImage) return;
    
    setIsAnalyzing(true);
    
    try {
      await new Promise(resolve => setTimeout(resolve, 3000));
      
      const mockAnalysis = `🔍 ANÁLISE DO STORY:

💡 PONTOS DE INTERESSE:
- A pessoa está em um show, o que indica gosto por música ao vivo.
- A legenda "Melhor noite!" mostra que a pessoa está se divertindo.
- A camiseta da banda "The Strokes" é uma ótima oportunidade para iniciar uma conversa.

🎯 SUGESTÕES DE RESPOSTA:
1. "Que show incrível! Também sou fã de The Strokes. Qual sua música preferida deles?"
2. "Essa noite parece ter sido épica! Qual foi o ponto alto do show?"
3. "Uau, que energia! Me conta mais sobre essa noite."

🔥 DICA DE OURO:
Mostre que você prestou atenção nos detalhes da foto. Isso demonstra interesse genuíno e aumenta suas chances de uma resposta positiva.`;
      
      setAnalysis(mockAnalysis);
      
    } catch (error) {
      console.error('Erro ao analisar:', error);
    } finally {
      setIsAnalyzing(false);
    }
  };

  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (err) {
      console.error('Erro ao copiar:', err);
    }
  };

  return (
    <motion.div 
      className="min-h-screen p-4 bg-gradient-to-br from-pink-900 via-black to-purple-900 text-white relative overflow-hidden"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.5 }}
    >
      <div className="relative z-10 max-w-md mx-auto">
        {/* Header */}
        <div className="flex items-center mb-8 pt-8">
          <motion.button 
            onClick={onBack}
            className="mr-4 p-3 rounded-full hover:bg-gray-800/50 transition-all cursor-hover"
            whileHover={{ scale: 1.1, rotate: -10 }}
            whileTap={{ scale: 0.9 }}
          >
            <ChevronLeft className="w-6 h-6" />
          </motion.button>
          <div>
            <h1 className="text-2xl font-bold">Análise de Story 🧙</h1>
            <p className="text-sm text-gray-400">Receba a resposta ideal para qualquer story</p>
          </div>
        </div>

        <div className="space-y-6">
          {/* Upload Section */}
          <GlassCard>
            <div className="space-y-4">
              <h3 className="font-bold text-lg flex items-center gap-2">
                <Upload className="w-5 h-5" />
                Envie o Print do Story
              </h3>
              
              <div className="border-2 border-dashed border-gray-600 rounded-xl p-8 text-center">
                <input
                  type="file"
                  accept="image/*"
                  onChange={handleImageUpload}
                  className="hidden"
                  id="image-upload"
                />
                <label htmlFor="image-upload" className="cursor-pointer">
                  {uploadedImage ? (
                    <img 
                      src={uploadedImage} 
                      alt="Story" 
                      className="max-w-full h-48 object-contain mx-auto rounded-lg"
                    />
                  ) : (
                    <div className="space-y-3">
                      <Upload className="w-12 h-12 mx-auto text-gray-400" />
                      <p className="text-gray-400">Clique para fazer upload do print do story</p>
                    </div>
                  )}
                </label>
              </div>
            </div>
          </GlassCard>

          {/* Analyze Button */}
          {uploadedImage && (
            <GlowingButton
              onClick={analyzeStory}
              disabled={isAnalyzing}
              className="w-full py-4"
            >
              {isAnalyzing ? (
                <span className="flex items-center gap-2">
                  <Loader2 className="w-5 h-5 animate-spin" />
                  Analisando...
                </span>
              ) : (
                <span className="flex items-center gap-2">
                  <FileText className="w-5 h-5" />
                  Analisar Story
                </span>
              )}
            </GlowingButton>
          )}

          {/* Analysis Result */}
          {analysis && (
            <GlassCard>
              <div className="space-y-4">
                <h3 className="font-bold text-lg flex items-center gap-2">
                  <FileText className="w-5 h-5" />
                  Análise da IA
                </h3>
                <p className="text-gray-300 whitespace-pre-wrap">{analysis}</p>
                <GlowingButton
                  onClick={() => copyToClipboard(analysis)}
                  className="w-full py-3"
                >
                  {copied ? (
                    <span className="flex items-center gap-2">
                      <Check className="w-5 h-5" />
                      Copiado!
                    </span>
                  ) : (
                    <span className="flex items-center gap-2">
                      <Copy className="w-5 h-5" />
                      Copiar Análise
                    </span>
                  )}
                </GlowingButton>
              </div>
            </GlassCard>
          )}
        </div>
      </div>
    </motion.div>
  );
};

export default StoryAnalysisPage;
