import React, { useState, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  ChevronLeft, Upload, Send, Loader2, MessageCircle, Camera, Copy, Check, 
  Eye, Download, Share2, <PERSON>rkles, Zap, Heart, Star 
} from 'lucide-react';
import { GlassCard } from '../../components/glass-card';
import { GlowingButton } from '../../components/ui/glowing-effect';

interface AnalysisResult {
  sentiment: string;
  suggestions: string[];
  score: number;
}

const ChatAnalysisPage: React.FC<{ onBack: () => void }> = ({ onBack }) => {
  const [uploadedImage, setUploadedImage] = useState<string | null>(null);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [analysisResult, setAnalysisResult] = useState<AnalysisResult | null>(null);
  const [copiedIndex, setCopiedIndex] = useState<number | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        setUploadedImage(e.target?.result as string);
        setAnalysisResult(null);
      };
      reader.readAsDataURL(file);
    }
  };

  const analyzeConversation = async () => {
    if (!uploadedImage) return;

    setIsAnalyzing(true);
    
    try {
      // Simular análise com IA
      await new Promise(resolve => setTimeout(resolve, 3000));
      
      const mockAnalysis: AnalysisResult = {
        sentiment: 'Positivo com flerte sutil',
        suggestions: [
          '🔥 Resposta perfeita: "Haha, você é engraçada demais! Isso me lembrou de quando..."',
          '💡 Tente: "Adoro quando alguém tem esse tipo de humor! Qual é sua série favorita?"',
          '🎯 Dica: Use emojis para manter o tom leve e divertido',
          '⚡ Sugestão: Pergunte algo sobre o que ela mencionou para mostrar interesse genuíno',
        ],
        score: 85,
      };
      
      setAnalysisResult(mockAnalysis);
      
    } catch (error) {
      console.error('Erro ao analisar:', error);
    } finally {
      setIsAnalyzing(false);
    }
  };

  const copyToClipboard = async (text: string, index: number) => {
    try {
      await navigator.clipboard.writeText(text);
      setCopiedIndex(index);
      setTimeout(() => setCopiedIndex(null), 2000);
    } catch (err) {
      console.error('Erro ao copiar:', err);
    }
  };

  const downloadAnalysis = () => {
    // Implementar download da análise
    console.log('Baixando análise...');
  };

  const shareAnalysis = () => {
    // Implementar compartilhamento
    console.log('Compartilhando análise...');
  };

  return (
    <motion.div 
      className="min-h-screen p-4 bg-gradient-to-br from-green-900 via-black to-blue-900 text-white relative overflow-hidden"
      initial={{ x: 300, opacity: 0 }}
      animate={{ x: 0, opacity: 1 }}
      exit={{ x: -300, opacity: 0 }}
    >
      <div className="relative z-10 max-w-md mx-auto">
        {/* Header */}
        <div className="flex items-center mb-8 pt-8">
          <motion.button 
            onClick={onBack}
            className="mr-4 p-3 rounded-full hover:bg-gray-800/50 transition-all"
            whileHover={{ scale: 1.1, rotate: -10 }}
            whileTap={{ scale: 0.9 }}
          >
            <ChevronLeft className="w-6 h-6" />
          </motion.button>
          <div>
            <h1 className="text-2xl font-bold">Análise de Conversas 🔍</h1>
            <p className="text-sm text-gray-400">IA analisa seus prints</p>
          </div>
        </div>

        <div className="space-y-6">
          {/* Upload Section */}
          <GlassCard>
            <h2 className="text-lg font-bold mb-4 flex items-center gap-2">
              <Upload className="w-5 h-5 text-blue-400" />
              Envie seu Print
            </h2>
            
            <div 
              className="border-2 border-dashed border-gray-600 rounded-xl p-8 text-center cursor-pointer hover:border-gray-500 transition-all"
              onClick={() => fileInputRef.current?.click()}
            >
              <input
                ref={fileInputRef}
                type="file"
                accept="image/*"
                onChange={handleImageUpload}
                className="hidden"
              />
              
              {uploadedImage ? (
                <div className="space-y-4">
                  <img 
                    src={uploadedImage} 
                    alt="Conversa" 
                    className="max-w-full h-64 object-contain rounded-lg mx-auto"
                  />
                  <p className="text-sm text-gray-400">Clique para trocar a imagem</p>
                </div>
              ) : (
                <div className="space-y-4">
                  <Camera className="w-12 h-12 mx-auto text-gray-400" />
                  <div>
                    <p className="text-lg font-medium">Arraste ou clique para enviar</p>
                    <p className="text-sm text-gray-400">Formatos: JPG, PNG, WebP</p>
                  </div>
                </div>
              )}
            </div>
          </GlassCard>

          {/* Analyze Button */}
          {uploadedImage && (
            <GlowingButton
              onClick={analyzeConversation}
              disabled={isAnalyzing}
              className="w-full py-4 text-lg"
            >
              {isAnalyzing ? (
                <>
                  <Loader2 className="w-5 h-5 mr-2 animate-spin" />
                  Analisando com IA...
                </>
              ) : (
                <>
                  <Eye className="w-5 h-5 mr-2" />
                  Analisar Conversa 🔍
                </>
              )}
            </GlowingButton>
          )}

          {/* Analysis Results */}
          <AnimatePresence>
            {analysisResult && (
              <motion.div
                initial={{ opacity: 0, y: 50 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -50 }}
                className="space-y-6"
              >
                {/* Score */}
                <GlassCard className="text-center">
                  <h3 className="text-lg font-bold mb-2">Pontuação da Conversa</h3>
                  <div className="text-4xl font-bold text-green-400">
                    {analysisResult.score}%
                  </div>
                  <p className="text-sm text-gray-400 mt-2">{analysisResult.sentiment}</p>
                </GlassCard>

                {/* Suggestions */}
                <GlassCard>
                  <h3 className="text-lg font-bold mb-4 flex items-center gap-2">
                    <Sparkles className="w-5 h-5 text-yellow-400" />
                    Sugestões de Respostas
                  </h3>
                  
                  <div className="space-y-3">
                    {analysisResult.suggestions.map((suggestion, index) => (
                      <motion.div
                        key={index}
                        initial={{ opacity: 0, x: -20 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ delay: index * 0.1 }}
                        className="p-4 bg-gray-800/50 rounded-lg flex items-center justify-between group"
                      >
                        <p className="text-sm text-gray-200 flex-1">{suggestion}</p>
                        <motion.button
                          onClick={() => copyToClipboard(suggestion, index)}
                          className="ml-3 p-2 rounded-lg hover:bg-gray-700 transition-all"
                          whileHover={{ scale: 1.1 }}
                          whileTap={{ scale: 0.9 }}
                        >
                          {copiedIndex === index ? (
                            <Check className="w-4 h-4 text-green-500" />
                          ) : (
                            <Copy className="w-4 h-4 text-gray-400 group-hover:text-white" />
                          )}
                        </motion.button>
                      </motion.div>
                    ))}
                  </div>
                </GlassCard>

                {/* Actions */}
                <div className="flex gap-3">
                  <motion.button
                    onClick={downloadAnalysis}
                    className="flex-1 p-3 bg-blue-500/20 border border-blue-500/30 rounded-xl flex items-center justify-center gap-2 hover:bg-blue-500/30 transition-all"
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                  >
                    <Download className="w-4 h-4" />
                    <span className="text-sm">Baixar</span>
                  </motion.button>
                  
                  <motion.button
                    onClick={shareAnalysis}
                    className="flex-1 p-3 bg-green-500/20 border border-green-500/30 rounded-xl flex items-center justify-center gap-2 hover:bg-green-500/30 transition-all"
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                  >
                    <Share2 className="w-4 h-4" />
                    <span className="text-sm">Compartilhar</span>
                  </motion.button>
                </div>
              </motion.div>
            )}
          </AnimatePresence>
        </div>
      </div>
    </motion.div>
  );
};

export default ChatAnalysisPage;
