import React, { useState } from 'react';
import { GlassCard } from '../../components/glass-card';
import { Button } from '../../components/ui/button';
import { motion } from 'framer-motion';
import { <PERSON>rk<PERSON>, Copy, Check } from 'lucide-react';

const ideiasExemplo = [
  'Pergunte sobre viagens dos sonhos',
  'Fale de um filme ou série que você ama',
  'Conte uma curiosidade engraçada',
  'Pergunte sobre música favorita',
  'Sugira um desafio divertido',
  'Fale de um hobby inusitado',
  'Pergunte sobre um sonho de infância',
  'Fale de um lugar que quer conhecer',
];

const TopicsPage = ({
  generatedIdeas = ideiasExemplo,
  onGenerateIdeas = () => {},
  onCopy = () => {},
  onBack = () => {},
  ...props
}) => {
  const [copiedIndex, setCopiedIndex] = useState<number | null>(null);

  const handleCopy = (idea: string, idx: number) => {
    navigator.clipboard.writeText(idea);
    setCopiedIndex(idx);
    setTimeout(() => setCopiedIndex(null), 1200);
    onCopy(idea);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-black to-gray-900 flex flex-col items-center justify-center py-12 px-2">
      <motion.h1
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.7 }}
        className="text-4xl md:text-5xl font-extrabold text-transparent bg-clip-text bg-gradient-to-r from-orange-400 via-pink-500 to-yellow-400 mb-2 text-center"
      >
        Ideias de Conversa 🔥
      </motion.h1>
      <motion.p
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.7, delay: 0.2 }}
        className="mb-8 text-lg text-gray-300 text-center"
      >
        Para a conversa não morrer! Inspire-se com sugestões criativas:
      </motion.p>
      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-8 w-full max-w-5xl">
        {generatedIdeas.map((idea, idx) => (
          <GlassCard
            key={idx}
            variant="holographic"
            size="lg"
            glow
            floating
            className="flex flex-col items-center justify-between min-h-[180px]"
          >
            <div className="flex items-center gap-2 mb-2">
              <Sparkles className="w-5 h-5 text-orange-400 animate-pulse" />
              <span className="font-semibold text-lg text-orange-100">Sugestão</span>
            </div>
            <p className="text-center text-lg text-white mb-4">{idea}</p>
            <Button
              onClick={() => handleCopy(idea, idx)}
              className="flex items-center gap-2 px-4 py-2 rounded-xl bg-gradient-to-r from-orange-500 to-pink-500 text-white font-bold shadow hover:scale-105 transition-transform"
            >
              {copiedIndex === idx ? <Check className="w-5 h-5 text-green-300" /> : <Copy className="w-5 h-5" />}
              {copiedIndex === idx ? 'Copiado!' : 'Copiar'}
            </Button>
          </GlassCard>
        ))}
      </div>
      <div className="flex flex-col sm:flex-row gap-4 mt-12">
        <Button
          onClick={onGenerateIdeas}
          className="px-8 py-3 rounded-2xl bg-gradient-to-r from-orange-500 via-pink-500 to-yellow-400 text-white font-bold text-lg shadow-lg hover:scale-105 transition-transform"
        >
          Gerar Novas Ideias
        </Button>
        <Button
          onClick={onBack}
          variant="outline"
          className="px-8 py-3 rounded-2xl border-orange-400 text-orange-200 hover:bg-orange-500/10 text-lg font-bold transition-all"
        >
          Voltar
        </Button>
      </div>
    </div>
  );
};

export default TopicsPage; 