import React from 'react';
import { motion } from 'framer-motion';
import { cn } from '../../lib/utils';

export const SquaresBackground = () => {
  const squares = Array.from(Array(30));

  return (
    <div className="absolute inset-0 h-full w-full overflow-hidden">
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 0.5, transition: { duration: 1 } }}
        className="absolute inset-0 h-full w-full"
      >
        {squares.map((_, i) => {
          const size = Math.random() * 0.75 + 0.25;
          const style = {
            width: `${size}rem`,
            height: `${size}rem`,
            top: `${Math.random() * 100}%`,
            left: `${Math.random() * 100}%`,
            animationDelay: `${Math.random() * 2}s`,
          };

          return (
            <motion.div
              key={i}
              className={cn(
                'absolute rounded-full bg-white/10 animate-pulse',
              )}
              style={style}
              initial={{ scale: 0, opacity: 0 }}
              animate={{ scale: [0, 1, 0.5, 1], opacity: [0, 1, 0.5, 1] }}
              transition={{
                duration: Math.random() * 2 + 1,
                repeat: Infinity,
                repeatType: 'loop',
              }}
            />
          );
        })}
      </motion.div>
    </div>
  );
};