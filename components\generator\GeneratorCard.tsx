import React from 'react';
import { motion } from 'framer-motion';
import { ArrowRight } from 'lucide-react';
import { GlowingButton } from '../ui/glowing-effect';

interface GeneratorCardProps {
  icon: React.ReactNode;
  title: string;
  description: string;
  features: string[];
  onClick: () => void;
  color: string;
}

export const GeneratorCard: React.FC<GeneratorCardProps> = ({ icon, title, description, features, onClick, color }) => {
  return (
    <motion.div
      whileHover={{ scale: 1.02 }}
      className={`bg-gray-900/50 border border-${color}-500/30 rounded-2xl p-6 flex flex-col h-full`}
    >
      <div className="flex items-center gap-4 mb-4">
        <div className={`w-12 h-12 bg-${color}-500/10 rounded-lg flex items-center justify-center text-${color}-400`}>
          {icon}
        </div>
        <div>
          <h3 className="text-lg font-bold text-white">{title}</h3>
          <p className="text-sm text-gray-400">{description}</p>
        </div>
      </div>
      <ul className="space-y-2 text-sm text-gray-300 my-4 flex-grow">
        {features.map((feature, i) => (
          <li key={i} className="flex items-start gap-2">
            <span className={`text-${color}-400 mt-1`}>✓</span>
            <span>{feature}</span>
          </li>
        ))}
      </ul>
      <GlowingButton
        onClick={onClick}
        className={`mt-auto w-full flex items-center justify-center gap-2 bg-${color}-600 hover:bg-${color}-700`}
      >
        <span>Usar Ferramenta</span>
        <ArrowRight className="w-4 h-4" />
      </GlowingButton>
    </motion.div>
  );
};