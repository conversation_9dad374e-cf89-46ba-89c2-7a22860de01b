
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 3.9%;
    --foreground: 0 0% 98%;
    --card: 0 0% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 0 0% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 0 0% 98%;
    --primary-foreground: 0 0% 9%;
    --secondary: 0 0% 14.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 0 0% 14.9%;
    --muted-foreground: 0 0% 63.9%;
    --accent: 0 0% 14.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 14.9%;
    --input: 0 0% 14.9%;
    --ring: 0 0% 83.1%;
    --radius: 0.5rem;

    /* Enhanced 3D and Animation Variables */
    --glow-primary: 255, 61, 0;
    --glow-secondary: 147, 51, 234;
    --glow-accent: 6, 182, 212;
    --glass-bg: rgba(255, 255, 255, 0.05);
    --glass-border: rgba(255, 255, 255, 0.1);
    --glass-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    --perspective: 1000px;
    --transform-speed: 0.3s;
  }

  .dark {
    --background: 0 0% 3.9%;
    --foreground: 0 0% 98%;
    --card: 0 0% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 0 0% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 0 0% 98%;
    --primary-foreground: 0 0% 9%;
    --secondary: 0 0% 14.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 0 0% 14.9%;
    --muted-foreground: 0 0% 63.9%;
    --accent: 0 0% 14.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 14.9%;
    --input: 0 0% 14.9%;
    --ring: 0 0% 83.1%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  html {
    scroll-behavior: smooth;
  }

  body {
    @apply bg-background text-foreground;
    font-family: 'Inter', sans-serif;
    perspective: var(--perspective);
    overflow-x: hidden;
  }

  /* Enhanced Scrollbar with 3D effects */
  ::-webkit-scrollbar {
    width: 12px;
  }
  ::-webkit-scrollbar-track {
    background: linear-gradient(180deg, rgba(0,0,0,0.1) 0%, rgba(255,61,0,0.05) 100%);
    border-radius: 6px;
  }
  ::-webkit-scrollbar-thumb {
    background: linear-gradient(180deg, rgba(255,61,0,0.8) 0%, rgba(255,87,34,0.6) 100%);
    border-radius: 6px;
    border: 2px solid transparent;
    background-clip: content-box;
    box-shadow: 0 0 10px rgba(255,61,0,0.3);
    transition: all 0.3s ease;
  }
  ::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(180deg, rgba(255,61,0,1) 0%, rgba(255,87,34,0.8) 100%);
    box-shadow: 0 0 20px rgba(255,61,0,0.5);
    transform: scale(1.1);
  }
}

/* 3D Effects and Glassmorphism Utilities */
@layer utilities {
  /* Glass Effects */
  .glass {
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    box-shadow: var(--glass-shadow);
  }

  .glass-strong {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(30px);
    -webkit-backdrop-filter: blur(30px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.4);
  }

  .glass-subtle {
    background: rgba(255, 255, 255, 0.02);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.05);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
  }

  /* 3D Transform Utilities */
  .transform-3d {
    transform-style: preserve-3d;
    perspective: var(--perspective);
  }

  .rotate-x-12 {
    transform: rotateX(12deg);
  }

  .rotate-y-12 {
    transform: rotateY(12deg);
  }

  .translate-z-10 {
    transform: translateZ(10px);
  }

  .translate-z-20 {
    transform: translateZ(20px);
  }

  .translate-z-50 {
    transform: translateZ(50px);
  }

  /* Glow Effects */
  .glow-primary {
    box-shadow: 0 0 20px rgba(var(--glow-primary), 0.3);
  }

  .glow-primary-strong {
    box-shadow: 0 0 40px rgba(var(--glow-primary), 0.6);
  }

  .glow-secondary {
    box-shadow: 0 0 20px rgba(var(--glow-secondary), 0.3);
  }

  .glow-accent {
    box-shadow: 0 0 20px rgba(var(--glow-accent), 0.3);
  }

  /* Hover 3D Effects */
  .hover-lift {
    transition: all var(--transform-speed) ease;
  }

  .hover-lift:hover {
    transform: translateY(-8px) translateZ(20px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
  }

  .hover-tilt {
    transition: all var(--transform-speed) ease;
  }

  .hover-tilt:hover {
    transform: rotateX(-5deg) rotateY(5deg) translateZ(10px);
  }

  .hover-float {
    animation: float 3s ease-in-out infinite;
  }

  .hover-pulse-glow {
    animation: pulseGlow 2s ease-in-out infinite;
  }
}

/* Advanced Keyframe Animations */
@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotateX(0deg);
  }
  50% {
    transform: translateY(-10px) rotateX(2deg);
  }
}

@keyframes pulseGlow {
  0%, 100% {
    box-shadow: 0 0 20px rgba(var(--glow-primary), 0.3);
  }
  50% {
    box-shadow: 0 0 40px rgba(var(--glow-primary), 0.6);
  }
}

@keyframes shimmerMove {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

@keyframes rotateGlow {
  0% {
    transform: rotate(0deg);
    filter: hue-rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
    filter: hue-rotate(360deg);
  }
}

@keyframes morphBackground {
  0%, 100% {
    border-radius: 20px;
    background: linear-gradient(45deg, rgba(255,61,0,0.1), rgba(255,87,34,0.1));
  }
  25% {
    border-radius: 30px 10px 30px 10px;
    background: linear-gradient(135deg, rgba(147,51,234,0.1), rgba(236,72,153,0.1));
  }
  50% {
    border-radius: 10px 30px 10px 30px;
    background: linear-gradient(225deg, rgba(6,182,212,0.1), rgba(59,130,246,0.1));
  }
  75% {
    border-radius: 25px 15px 25px 15px;
    background: linear-gradient(315deg, rgba(34,197,94,0.1), rgba(16,185,129,0.1));
  }
}

@keyframes particleFloat {
  0% {
    transform: translateY(100vh) translateX(0px) rotate(0deg);
    opacity: 0;
  }
  10% {
    opacity: 1;
  }
  90% {
    opacity: 1;
  }
  100% {
    transform: translateY(-100px) translateX(100px) rotate(360deg);
    opacity: 0;
  }
}

@keyframes holographicShift {
  0%, 100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}

@keyframes liquidMove {
  0%, 100% {
    transform: translateX(0%) rotateZ(0deg);
  }
  25% {
    transform: translateX(5%) rotateZ(1deg);
  }
  50% {
    transform: translateX(-5%) rotateZ(-1deg);
  }
  75% {
    transform: translateX(3%) rotateZ(0.5deg);
  }
}

/* Particle System Utilities */
.particle-system {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 1;
}

.particle {
  position: absolute;
  width: 4px;
  height: 4px;
  background: radial-gradient(circle, rgba(255,61,0,0.8) 0%, transparent 70%);
  border-radius: 50%;
  animation: particleFloat 15s linear infinite;
}

.particle:nth-child(2n) {
  background: radial-gradient(circle, rgba(147,51,234,0.8) 0%, transparent 70%);
  animation-duration: 18s;
  animation-delay: -5s;
}

.particle:nth-child(3n) {
  background: radial-gradient(circle, rgba(6,182,212,0.8) 0%, transparent 70%);
  animation-duration: 12s;
  animation-delay: -10s;
}

/* Interactive Elements */
.interactive-card {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  transform-style: preserve-3d;
}

.interactive-card:hover {
  transform: translateY(-8px) rotateX(-5deg) rotateY(5deg);
  box-shadow:
    0 25px 50px rgba(0, 0, 0, 0.3),
    0 0 30px rgba(var(--glow-primary), 0.2);
}

.holographic-text {
  background: linear-gradient(
    45deg,
    #ff3d00,
    #ff5722,
    #9c27b0,
    #673ab7,
    #3f51b5,
    #2196f3,
    #00bcd4,
    #009688,
    #4caf50,
    #8bc34a,
    #cddc39,
    #ffeb3b,
    #ffc107,
    #ff9800,
    #ff5722,
    #ff3d00
  );
  background-size: 400% 400%;
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  animation: holographicShift 3s ease-in-out infinite;
}

.liquid-background {
  background: linear-gradient(45deg,
    rgba(255,61,0,0.1),
    rgba(255,87,34,0.1),
    rgba(147,51,234,0.1),
    rgba(6,182,212,0.1)
  );
  background-size: 400% 400%;
  animation: morphBackground 8s ease-in-out infinite;
}

.shimmer-effect {
  position: relative;
  overflow: hidden;
}

.shimmer-effect::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  animation: shimmerMove 2s infinite;
}

/* Responsive 3D Effects */
@media (max-width: 768px) {
  .transform-3d {
    perspective: 500px;
  }

  .hover-lift:hover {
    transform: translateY(-4px);
  }

  .hover-tilt:hover {
    transform: rotateX(-2deg) rotateY(2deg);
  }

  .interactive-card:hover {
    transform: translateY(-4px) rotateX(-2deg) rotateY(2deg);
  }
}

/* Performance Optimizations */
.gpu-accelerated {
  transform: translateZ(0);
  will-change: transform;
}

.smooth-animation {
  animation-fill-mode: both;
  animation-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}
