import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Eye, EyeOff, Mail, Lock, User, Flame, Sparkles, Heart, Star, Crown } from 'lucide-react';
import { Page } from '../../types';
import { GlassCard } from '../../components/glass-card';
import { FluidBlob } from '../../components/fluid-blob';
import { LiquidButton } from '../../components/liquid-glass-button';
import { GradientButton } from '../../components/gradient-button';
import { GlowingEffect } from '../../components/glowing-effect';
import { VapourTextEffect } from '../../components/vapour-text-effect';
import { Gender } from '../../types';

interface GenderSelectionPageProps {
  onGenderSelect: (gender: Gender) => void;
}

export const GenderSelectionPage: React.FC<GenderSelectionPageProps> = ({ onGenderSelect }) => {
  return (
    <div className="min-h-screen flex flex-col items-center justify-center p-4 text-center bg-background text-text-primary">
      <motion.div
        initial={{ opacity: 0, scale: 0.8 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ duration: 0.5 }}
        className="mb-8"
      >
        <img src="/logo.png" alt="PapoFire Logo" className="w-24 h-24" />
      </motion.div>
      
      <motion.h1 
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.2 }}
        className="text-4xl font-bold mb-2"
      >
        Quase lá!
      </motion.h1>
      
      <motion.p 
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.3 }}
        className="text-text-secondary text-lg mb-12"
      >
        Para te dar as melhores respostas, precisamos saber:
      </motion.p>
      
      <div className="flex flex-col sm:flex-row gap-8">
        <motion.button 
          onClick={() => onGenderSelect(Gender.Male)}
          className="bg-background-secondary border-2 border-brand-blue text-white font-bold rounded-2xl p-8 w-64 h-64 flex flex-col justify-center items-center gap-4 hover:bg-blue-500/20 hover:border-blue-400 transition-all duration-300"
          whileHover={{ scale: 1.05, boxShadow: '0 0 25px rgba(59, 130, 246, 0.5)' }}
          whileTap={{ scale: 0.95 }}
          initial={{ opacity: 0, x: -50 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: 0.4 }}
        >
          <span className="text-8xl">👨</span>
          <span className="text-3xl">Homem</span>
        </motion.button>
        
        <motion.button 
          onClick={() => onGenderSelect(Gender.Female)}
          className="bg-background-secondary border-2 border-brand-pink text-white font-bold rounded-2xl p-8 w-64 h-64 flex flex-col justify-center items-center gap-4 hover:bg-pink-500/20 hover:border-pink-400 transition-all duration-300"
          whileHover={{ scale: 1.05, boxShadow: '0 0 25px rgba(236, 72, 153, 0.5)' }}
          whileTap={{ scale: 0.95 }}
          initial={{ opacity: 0, x: 50 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: 0.5 }}
        >
          <span className="text-8xl">👩</span>
          <span className="text-3xl">Mulher</span>
        </motion.button>
      </div>
    </div>
  );
};

interface AuthPageProps {
  onLogin: () => void;
  onBack: () => void;
}

const AuthPage: React.FC<AuthPageProps> = ({ onLogin, onBack }) => {
  const [isLogin, setIsLogin] = useState(true);
  const [showPassword, setShowPassword] = useState(false);
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    password: '',
    confirmPassword: ''
  });
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });
  const [isLoaded, setIsLoaded] = useState(false);

  useEffect(() => {
    setIsLoaded(true);
    
    const handleMouseMove = (e: MouseEvent) => {
      setMousePosition({ x: e.clientX, y: e.clientY });
    };

    window.addEventListener('mousemove', handleMouseMove);
    return () => window.removeEventListener('mousemove', handleMouseMove);
  }, []);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    // Simular autenticação
    setTimeout(() => {
      onLogin();
    }, 1000);
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    });
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-black to-gray-800 relative overflow-hidden">
      {/* Animated background with 4D effects */}
      <div className="absolute inset-0 overflow-hidden">
        <FluidBlob 
          size="xl" 
          color="gradient" 
          animated={true}
          className="absolute top-10 left-10 opacity-20" 
        />
        <FluidBlob 
          size="lg" 
          color="purple" 
          animated={true}
          className="absolute top-1/3 right-20 opacity-15" 
        />
        <FluidBlob 
          size="md" 
          color="orange" 
          animated={true}
          className="absolute bottom-20 left-1/4 opacity-25" 
        />
        
        {/* Floating particles */}
        <div className="absolute inset-0 pointer-events-none">
          {[...Array(25)].map((_, i) => (
            <motion.div
              key={i}
              className="absolute w-1 h-1 bg-purple-400/30 rounded-full"
              style={{
                left: `${Math.random() * 100}%`,
                top: `${Math.random() * 100}%`
              }}
              animate={{
                y: [-20, 20, -20],
                x: [-10, 10, -10],
                opacity: [0.3, 0.8, 0.3],
                scale: [0.5, 1.5, 0.5]
              }}
              transition={{
                duration: 4 + i * 0.2,
                repeat: Infinity,
                ease: "easeInOut"
              }}
            />
          ))}
        </div>
      </div>

      {/* Mouse follower effect */}
      <motion.div
        className="fixed w-96 h-96 rounded-full bg-purple-500/10 blur-3xl pointer-events-none z-0"
        style={{
          left: mousePosition.x - 192,
          top: mousePosition.y - 192
        }}
        animate={{
          scale: [1, 1.2, 1],
          opacity: [0.1, 0.3, 0.1]
        }}
        transition={{
          duration: 3,
          repeat: Infinity,
          ease: "easeInOut"
        }}
      />

      <div className="relative z-10 container mx-auto px-6 py-12 flex items-center justify-center min-h-screen">
        <motion.div
          className="w-full max-w-md"
          initial={{ opacity: 0, scale: 0.9, y: 30 }}
          animate={{ opacity: isLoaded ? 1 : 0, scale: isLoaded ? 1 : 0.9, y: isLoaded ? 0 : 30 }}
          transition={{ duration: 1 }}
        >
          {/* Header */}
          <motion.div
            className="text-center mb-8"
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
          >
            <GlowingEffect color="orange" size="lg" className="mb-4 inline-block">
              <Flame className="w-12 h-12 text-orange-400" />
            </GlowingEffect>
            
            <VapourTextEffect 
              text="PapoFire" 
              color="fire" 
              size="2xl" 
              className="mb-2 font-bold"
            />
            
            <p className="text-gray-400">
              {isLogin ? 'Entre na sua conta' : 'Crie sua conta'}
            </p>
          </motion.div>

          {/* Auth Form */}
          <GlassCard
            variant="holographic"
            size="xl"
            glow={true}
            floating={true}
            className="relative overflow-hidden"
          >
            {/* Animated background particles */}
            <div className="absolute inset-0 pointer-events-none">
              {[...Array(10)].map((_, i) => (
                <motion.div
                  key={i}
                  className="absolute w-1 h-1 bg-purple-400/40 rounded-full"
                  style={{
                    left: `${Math.random() * 100}%`,
                    top: `${Math.random() * 100}%`
                  }}
                  animate={{
                    y: [-5, 5, -5],
                    x: [-3, 3, -3],
                    opacity: [0.4, 0.8, 0.4],
                    scale: [0.5, 1, 0.5]
                  }}
                  transition={{
                    duration: 2 + i * 0.1,
                    repeat: Infinity,
                    ease: "easeInOut"
                  }}
                />
              ))}
            </div>

            <div className="relative z-10">
              {/* Toggle Buttons */}
              <div className="flex mb-8">
                <GlassCard
                  variant="glass"
                  size="sm"
                  className="flex rounded-lg p-1 w-full"
                >
                  <motion.button
                    className={`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-all ${
                      isLogin 
                        ? 'bg-purple-500/30 text-white' 
                        : 'text-gray-400 hover:text-white'
                    }`}
                    onClick={() => setIsLogin(true)}
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                  >
                    Login
                  </motion.button>
                  <motion.button
                    className={`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-all ${
                      !isLogin 
                        ? 'bg-purple-500/30 text-white' 
                        : 'text-gray-400 hover:text-white'
                    }`}
                    onClick={() => setIsLogin(false)}
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                  >
                    Cadastro
                  </motion.button>
                </GlassCard>
              </div>

              {/* Form */}
              <form onSubmit={handleSubmit} className="space-y-6">
                {/* Name Field (only for signup) */}
                {!isLogin && (
                  <motion.div
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    exit={{ opacity: 0, x: -20 }}
                    transition={{ duration: 0.3 }}
                  >
                    <label className="block text-sm font-medium text-gray-300 mb-2">
                      Nome
                    </label>
                    <div className="relative">
                      <GlassCard
                        variant="glass"
                        size="sm"
                        className="flex items-center"
                      >
                        <User className="w-5 h-5 text-gray-400 mr-3" />
                        <input
                          type="text"
                          name="name"
                          value={formData.name}
                          onChange={handleInputChange}
                          className="flex-1 bg-transparent border-none outline-none text-white placeholder-gray-400"
                          placeholder="Seu nome"
                          required={!isLogin}
                        />
                      </GlassCard>
                    </div>
                  </motion.div>
                )}

                {/* Email Field */}
                <motion.div
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: 0.1 }}
                >
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    Email
                  </label>
                  <div className="relative">
                    <GlassCard
                      variant="glass"
                      size="sm"
                      className="flex items-center"
                    >
                      <Mail className="w-5 h-5 text-gray-400 mr-3" />
                      <input
                        type="email"
                        name="email"
                        value={formData.email}
                        onChange={handleInputChange}
                        className="flex-1 bg-transparent border-none outline-none text-white placeholder-gray-400"
                        placeholder="<EMAIL>"
                        required
                      />
                    </GlassCard>
                  </div>
                </motion.div>

                {/* Password Field */}
                <motion.div
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: 0.2 }}
                >
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    Senha
                  </label>
                  <div className="relative">
                    <GlassCard
                      variant="glass"
                      size="sm"
                      className="flex items-center"
                    >
                      <Lock className="w-5 h-5 text-gray-400 mr-3" />
                      <input
                        type={showPassword ? 'text' : 'password'}
                        name="password"
                        value={formData.password}
                        onChange={handleInputChange}
                        className="flex-1 bg-transparent border-none outline-none text-white placeholder-gray-400"
                        placeholder="Sua senha"
                        required
                      />
                      <motion.button
                        type="button"
                        onClick={() => setShowPassword(!showPassword)}
                        className="text-gray-400 hover:text-white transition-colors"
                        whileHover={{ scale: 1.1 }}
                        whileTap={{ scale: 0.9 }}
                      >
                        {showPassword ? <EyeOff className="w-5 h-5" /> : <Eye className="w-5 h-5" />}
                      </motion.button>
                    </GlassCard>
                  </div>
                </motion.div>

                {/* Confirm Password Field (only for signup) */}
                {!isLogin && (
                  <motion.div
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    exit={{ opacity: 0, x: -20 }}
                    transition={{ duration: 0.3, delay: 0.3 }}
                  >
                    <label className="block text-sm font-medium text-gray-300 mb-2">
                      Confirmar Senha
                    </label>
                    <div className="relative">
                      <GlassCard
                        variant="glass"
                        size="sm"
                        className="flex items-center"
                      >
                        <Lock className="w-5 h-5 text-gray-400 mr-3" />
                        <input
                          type={showPassword ? 'text' : 'password'}
                          name="confirmPassword"
                          value={formData.confirmPassword}
                          onChange={handleInputChange}
                          className="flex-1 bg-transparent border-none outline-none text-white placeholder-gray-400"
                          placeholder="Confirme sua senha"
                          required={!isLogin}
                        />
                      </GlassCard>
                    </div>
                  </motion.div>
                )}

                {/* Submit Button */}
                <motion.div
                  className="pt-4"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.4 }}
                >
                  <GradientButton
                    variant="cosmic"
                    size="xl"
                    type="submit"
                    className="w-full"
                  >
                    <GlowingEffect color="purple" size="sm" className="mr-2">
                      {isLogin ? <Star className="w-5 h-5" /> : <Crown className="w-5 h-5" />}
                    </GlowingEffect>
                    {isLogin ? 'Entrar' : 'Criar Conta'}
                  </GradientButton>
                </motion.div>
              </form>

              {/* Forgot Password (only for login) */}
              {isLogin && (
                <motion.div
                  className="mt-6 text-center"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ delay: 0.5 }}
                >
                  <LiquidButton 
                    variant="ghost" 
                    size="sm"
                    className="text-gray-400 hover:text-white"
                  >
                    Esqueceu a senha?
                  </LiquidButton>
                </motion.div>
              )}

              {/* Social Login */}
              <motion.div
                className="mt-8"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.6 }}
              >
                <div className="relative">
                  <div className="absolute inset-0 flex items-center">
                    <div className="w-full border-t border-gray-600"></div>
                  </div>
                  <div className="relative flex justify-center text-sm">
                    <span className="px-2 bg-gray-900 text-gray-400">Ou continue com</span>
                  </div>
                </div>

                <div className="mt-6 grid grid-cols-2 gap-3">
                  <LiquidButton 
                    variant="secondary" 
                    size="lg"
                    className="w-full"
                  >
                    <svg className="w-5 h-5 mr-2" viewBox="0 0 24 24">
                      <path fill="currentColor" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
                      <path fill="currentColor" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
                      <path fill="currentColor" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
                      <path fill="currentColor" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
                    </svg>
                    Google
                  </LiquidButton>

                  <LiquidButton 
                    variant="secondary" 
                    size="lg"
                    className="w-full"
                  >
                    <svg className="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
                    </svg>
                    Facebook
                  </LiquidButton>
                </div>
              </motion.div>
            </div>
          </GlassCard>

          {/* Back Button */}
          <motion.div
            className="mt-8 text-center"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.7 }}
          >
            <LiquidButton 
              variant="ghost" 
              size="lg"
              onClick={onBack}
              className="text-gray-400 hover:text-white"
            >
              ← Voltar para início
            </LiquidButton>
          </motion.div>

          {/* Features Preview */}
          <motion.div
            className="mt-12"
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.8 }}
          >
            <GlassCard
              variant="glass"
              size="lg"
              glow={false}
              floating={true}
              className="text-center"
            >
              <VapourTextEffect 
                text="🔥 O que te espera" 
                color="fire" 
                size="lg" 
                className="mb-4"
              />

              <div className="grid grid-cols-2 gap-4 text-sm text-gray-300">
                <div className="flex items-center gap-2">
                  <Heart className="w-4 h-4 text-red-400" />
                  <span>Conversas que colam</span>
                </div>
                <div className="flex items-center gap-2">
                  <Sparkles className="w-4 h-4 text-yellow-400" />
                  <span>Temas únicos</span>
                </div>
                <div className="flex items-center gap-2">
                  <Star className="w-4 h-4 text-blue-400" />
                  <span>IA personalizada</span>
                </div>
                <div className="flex items-center gap-2">
                  <Crown className="w-4 h-4 text-purple-400" />
                  <span>Resultados reais</span>
                </div>
              </div>
            </GlassCard>
          </motion.div>
        </motion.div>
      </div>
    </div>
  );
};

export default AuthPage;
