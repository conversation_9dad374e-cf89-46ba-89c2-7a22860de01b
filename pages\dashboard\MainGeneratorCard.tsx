
import React, { useState } from 'react';
import { SocialPlatformId, ToneId, GeneratorMode } from '../../types';
import type { User, GenerationResult } from '../../types';
import { SOCIAL_PLATFORMS, TONES } from '../../constants';
import { LiquidGlassButton } from '../../components/ShadcnUI';
import { ResultDisplay } from '../../components/AppFeatures';
import { generateIdeas } from '../../services/geminiService';
import { cn } from '../../lib/utils';
import { Loader2 } from 'lucide-react';

const MainGeneratorCard: React.FC<{user: User}> = ({user}) => {
    const [platform, setPlatform] = useState<SocialPlatformId>(SocialPlatformId.WhatsApp);
    const [topic, setTopic] = useState('');
    const [results, setResults] = useState<GenerationResult[]>([]);
    const [isLoading, setIsLoading] = useState(false);
    const [copiedText, setCopiedText] = useState<string | null>(null);

     const handleGeneration = async (tone: ToneId) => {
        if (!topic) return;
        setIsLoading(true);
        setResults([]);
        try {
            const generated = await generateIdeas(platform, tone, user.gender, topic, GeneratorMode.Topic);
            setResults(generated);
        } catch(e) { console.error(e); } 
        finally { setIsLoading(false); }
    };
    
    const handleCopy = (text: string) => {
        navigator.clipboard.writeText(text);
        setCopiedText(text);
        setTimeout(() => setCopiedText(null), 2000);
    };

    return (
        <div className="bg-card-background border border-card-border rounded-2xl p-6 space-y-6">
            <div className="flex items-center space-x-3 border-b border-card-border pb-4">
                {SOCIAL_PLATFORMS.map(p => (
                    <button 
                        key={p.id} 
                        onClick={() => setPlatform(p.id)} 
                        className={cn(
                            'flex items-center gap-2 px-4 py-2 text-sm font-semibold rounded-lg transition-all',
                            platform === p.id 
                                ? `bg-gradient-to-r ${p.color} text-white shadow-lg`
                                : 'text-text-secondary hover:bg-white/5 border border-transparent hover:border-white/10'
                        )}
                    >
                        {p.icon}
                        {p.label}
                    </button>
                ))}
            </div>
            <textarea
                value={topic}
                onChange={(e) => setTopic(e.target.value)}
                placeholder="Sobre o que você quer conversar?"
                className="w-full p-4 bg-input-background rounded-lg border border-input-border focus:border-brand-orange focus:outline-none resize-none text-text-primary"
                rows={3}
            />
            <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-5 gap-3">
                {TONES.map(tone => (
                     <LiquidGlassButton 
                        key={tone.id} 
                        onClick={() => handleGeneration(tone.id)} 
                        disabled={isLoading || !topic} 
                        className={cn("w-full text-sm flex items-center justify-center gap-2", `bg-gradient-to-r ${tone.color}`)}
                    >
                        {tone.icon}
                        {tone.label}
                    </LiquidGlassButton>
                ))}
            </div>
            {results.length > 0 && !isLoading && <ResultDisplay results={results} isLoading={isLoading} onCopy={handleCopy} copiedText={copiedText}/>}
             {isLoading && (
                <div className="flex items-center justify-center text-center p-8 space-x-4">
                    <Loader2 className="w-8 h-8 animate-spin text-brand-orange" />
                    <p className="text-lg font-semibold text-text-secondary">A IA está no comando...</p>
                </div>
            )}
        </div>
    )
}

export default MainGeneratorCard;
