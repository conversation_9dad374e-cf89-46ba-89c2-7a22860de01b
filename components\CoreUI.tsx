
import React, { useState, useCallback, useRef, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Eye, EyeOff, Lock, Mail, X } from 'lucide-react';
import { FlameIcon } from '../constants';
import { LiquidGlassButton } from './ShadcnUI';

export const AuthModal: React.FC<{ onClose: () => void; onAuthSuccess: (email: string) => void }> = ({ onClose, onAuthSuccess }) => {
    const [isLogin, setIsLogin] = useState(true);
    const [email, setEmail] = useState('');
    const [password, setPassword] = useState('');
    const [error, setError] = useState('');
    const [showPassword, setShowPassword] = useState(false);

    const handleSubmit = useCallback(() => {
        setError('');
        if (!email.trim() || !password.trim()) {
            setError('Preencha todos os campos.');
            return;
        }
        // Mock auth logic
        onAuthSuccess(email);
    }, [email, password, onAuthSuccess]);
    
    return (
         <motion.div
            className="fixed inset-0 bg-black/80 backdrop-blur-md flex items-center justify-center z-50 p-4"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            onClick={onClose}
        >
            <motion.div
                className="bg-gradient-to-br from-gray-900 to-black rounded-3xl p-8 w-full max-w-md border border-white/10"
                initial={{ scale: 0.9, opacity: 0 }}
                animate={{ scale: 1, opacity: 1 }}
                exit={{ scale: 0.9, opacity: 0 }}
                onClick={(e) => e.stopPropagation()}
            >
                <div className="text-center mb-6">
                    <FlameIcon className="w-16 h-16 mx-auto mb-4 text-orange-500" />
                    <h2 className="text-3xl font-bold">{isLogin ? 'Entrar' : 'Criar Conta'}</h2>
                </div>

                <div className="flex bg-white/5 rounded-xl p-1 mb-6">
                    <button onClick={() => setIsLogin(true)} className={`flex-1 py-2 rounded-lg font-medium transition-all ${isLogin ? 'bg-orange-500' : 'hover:bg-white/5'}`}>Entrar</button>
                    <button onClick={() => setIsLogin(false)} className={`flex-1 py-2 rounded-lg font-medium transition-all ${!isLogin ? 'bg-orange-500' : 'hover:bg-white/5'}`}>Cadastrar</button>
                </div>

                <div className="space-y-4">
                     <div className="relative">
                        <Mail className="w-5 h-5 absolute left-3 top-1/2 -translate-y-1/2 text-gray-400" />
                        <input type="email" placeholder="Email" value={email} onChange={(e) => setEmail(e.target.value)} className="w-full pl-10 pr-4 py-3 bg-white/10 rounded-lg border border-transparent focus:border-orange-500 focus:outline-none" />
                    </div>
                     <div className="relative">
                        <Lock className="w-5 h-5 absolute left-3 top-1/2 -translate-y-1/2 text-gray-400" />
                        <input type={showPassword ? 'text' : 'password'} placeholder="Senha" value={password} onChange={(e) => setPassword(e.target.value)} className="w-full pl-10 pr-10 py-3 bg-white/10 rounded-lg border border-transparent focus:border-orange-500 focus:outline-none" />
                        <button onClick={() => setShowPassword(!showPassword)} className="absolute right-3 top-1/2 -translate-y-1/2 text-gray-400 hover:text-white">{showPassword ? <EyeOff/> : <Eye/>}</button>
                    </div>
                    {error && <p className="text-red-500 text-sm text-center">{error}</p>}
                    <LiquidGlassButton onClick={handleSubmit} className="w-full py-3">{isLogin ? 'Entrar' : 'Continuar'}</LiquidGlassButton>
                </div>
                <button onClick={onClose} className="absolute top-4 right-4 p-2 rounded-full hover:bg-white/10"><X/></button>
            </motion.div>
        </motion.div>
    );
};


const FireParticle: React.FC<{ initialY: number }> = ({ initialY }) => {
    const size = Math.random() * 3 + 1;
    const duration = Math.random() * 5 + 5;
    const initialX = Math.random() * 100;
    
    return (
        <motion.div
            className="absolute rounded-full bg-orange-500"
            style={{
                width: size,
                height: size,
                left: `${initialX}vw`,
                top: `${initialY}vh`,
                opacity: Math.random() * 0.5 + 0.2,
            }}
            animate={{
                y: -100,
                opacity: [0.5, 0],
                scale: [1, 0.5]
            }}
            transition={{
                duration,
                repeat: Number.POSITIVE_INFINITY,
                ease: "linear",
                delay: Math.random() * 5
            }}
        />
    );
};


export const FireParticles = () => {
    const particles = Array.from({ length: 15 }).map((_, i) => <FireParticle key={i} initialY={100 + Math.random() * 20} />);
    return <div className="fixed inset-0 pointer-events-none z-0 overflow-hidden">{particles}</div>;
};

export const SquaresBackground = () => {
    const squares = Array.from({ length: 15 }).map((_, i) => {
        const size = Math.random() * 3 + 1;
        const duration = Math.random() * 10 + 10;
        return (
            <motion.div
                key={i}
                className="absolute bg-white/5 rounded-sm"
                style={{
                    width: size,
                    height: size,
                    left: `${Math.random() * 100}vw`,
                    top: `${Math.random() * 100}vh`,
                }}
                animate={{
                    y: [0, Math.random() * 20 - 10],
                    x: [0, Math.random() * 20 - 10],
                    rotate: [0, Math.random() * 90],
                    opacity: [0, 1, 0]
                }}
                transition={{
                    duration,
                    repeat: Number.POSITIVE_INFINITY,
                    ease: "easeInOut",
                    delay: Math.random() * 5
                }}
            />
        );
    });
    return <div className="fixed inset-0 pointer-events-none z-0">{squares}</div>;
};