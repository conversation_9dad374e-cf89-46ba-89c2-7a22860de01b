import React, { useState } from 'react';
import { <PERSON><PERSON>2, <PERSON><PERSON><PERSON> } from 'lucide-react';
import { LiquidGlassButton } from '../ShadcnUI';
import { SocialPlatformSelector } from './SocialPlatformSelector';
import { ToneSelector } from './ToneSelector';
import { ResultDisplay } from './ResultDisplay';
import { SOCIAL_PLATFORMS, TONES } from '../../constants';
import { generateIdeas } from '../../services/geminiService';
import type { User, GenerationResult, SocialPlatformId, ToneId, GeneratorMode } from '../../types';

export const MainGeneratorCard: React.FC<{ user: User }> = ({ user }) => {
  const [platform, setPlatform] = useState<SocialPlatformId>(SocialPlatformId.WhatsApp);
  const [topic, setTopic] = useState('');
  const [results, setResults] = useState<GenerationResult[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [copiedText, setCopiedText] = useState<string | null>(null);

  const handleGeneration = async (tone: ToneId) => {
    if (!topic) return;
    
    setIsLoading(true);
    setResults([]);
    
    try {
      const generated = await generateIdeas(
        platform, 
        tone, 
        user.gender, 
        topic, 
        GeneratorMode.Topic
      );
      setResults(generated);
    } catch (error) {
      console.error('Error generating ideas:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleCopy = (text: string) => {
    navigator.clipboard.writeText(text);
    setCopiedText(text);
    setTimeout(() => setCopiedText(null), 2000);
  };

  return (
    <div className="bg-gradient-to-br from-orange-500/10 to-red-500/10 rounded-2xl p-1 shadow-lg border border-white/10">
      <div className="bg-gray-900/80 backdrop-blur-sm rounded-xl p-6">
        {/* Platform Tabs */}
        <div className="flex border-b border-white/10 mb-4 overflow-x-auto">
          {SOCIAL_PLATFORMS.map(p => (
            <button 
              key={p.id} 
              onClick={() => setPlatform(p.id)} 
              className={`px-4 py-2 text-sm font-medium transition-colors whitespace-nowrap ${
                platform === p.id 
                  ? 'text-orange-400 border-b-2 border-orange-400' 
                  : 'text-gray-400 hover:text-white'
              }`}
            >
              {p.label}
            </button>
          ))}
        </div>

        {/* Topic Input */}
        <textarea
          value={topic}
          onChange={(e) => setTopic(e.target.value)}
          placeholder="Sobre o que você quer conversar?"
          className="w-full p-3 bg-black/30 rounded-lg border border-white/10 focus:border-orange-500 focus:outline-none resize-none text-white placeholder-gray-500"
          rows={2}
        />

        {/* Tone Buttons */}
        <div className="mt-4 grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-5 gap-2">
          {TONES.map(tone => (
            <LiquidGlassButton 
              key={tone.id} 
              onClick={() => handleGeneration(tone.id)} 
              disabled={isLoading || !topic} 
              className="py-2 text-sm"
            >
              {tone.label}
            </LiquidGlassButton>
          ))}
        </div>

        {/* Results */}
        {results.length > 0 && !isLoading && (
          <ResultDisplay 
            results={results} 
            isLoading={isLoading} 
            onCopy={handleCopy} 
            copiedText={copiedText} 
          />
        )}

        {/* Loading */}
        {isLoading && (
          <div className="flex items-center justify-center text-center p-8 space-x-4">
            <Loader2 className="w-8 h-8 animate-spin text-orange-500" />
            <p className="text-lg font-semibold text-gray-300">A IA está no comando...</p>
          </div>
        )}
      </div>
    </div>
  );
};
