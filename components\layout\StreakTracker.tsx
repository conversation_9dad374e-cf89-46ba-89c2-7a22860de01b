import React from 'react';
import { <PERSON>, <PERSON>, Moon } from 'lucide-react';

interface StreakTrackerProps {
  streak: number;
}

export const StreakTracker: React.FC<StreakTrackerProps> = ({ streak = 3 }) => {
  return (
    <div className="p-4 rounded-xl bg-white/5 border border-white/10 flex items-center justify-between mb-8">
      <div className="flex items-center gap-3">
        <Flame className="text-orange-400 w-6 h-6" />
        <div>
          <p className="font-bold text-white">Streak de {streak} dias</p>
          <p className="text-xs text-gray-400">Continue assim pra não perder o fogo!</p>
        </div>
      </div>
      
      <div className="flex gap-1">
        {[...Array(5)].map((_, index) => (
          index < streak ? (
            <Sun key={index} className="w-5 h-5 text-orange-400" />
          ) : (
            <Moon key={index} className="w-5 h-5 text-gray-600" />
          )
        ))}
      </div>
    </div>
  );
};
