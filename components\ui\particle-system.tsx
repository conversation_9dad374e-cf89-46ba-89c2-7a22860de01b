import React, { useEffect, useState, useRef, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';

interface Particle {
  id: number;
  x: number;
  y: number;
  vx: number;
  vy: number;
  size: number;
  color: string;
  opacity: number;
  life: number;
  maxLife: number;
  type: 'fire' | 'spark' | 'glow' | 'bubble';
}

interface ParticleSystemProps {
  count?: number;
  colors?: string[];
  types?: Array<'fire' | 'spark' | 'glow' | 'bubble'>;
  intensity?: 'low' | 'medium' | 'high';
  interactive?: boolean;
  className?: string;
  variant?: 'floating' | 'explosion' | 'trail' | 'ambient';
}

export const ParticleSystem: React.FC<ParticleSystemProps> = ({
  count = 50,
  colors = ['#ff3d00', '#ff5722', '#9c27b0', '#673ab7', '#3f51b5', '#2196f3', '#00bcd4'],
  types = ['fire', 'spark', 'glow'],
  intensity = 'medium',
  interactive = false,
  className = '',
  variant = 'ambient',
}) => {
  const [particles, setParticles] = useState<Particle[]>([]);
  const [mousePos, setMousePos] = useState({ x: 0, y: 0 });
  const containerRef = useRef<HTMLDivElement>(null);
  const animationRef = useRef<number>();

  const intensityConfig = {
    low: { count: Math.floor(count * 0.5), speed: 0.5 },
    medium: { count, speed: 1 },
    high: { count: Math.floor(count * 1.5), speed: 1.5 },
  };

  const config = intensityConfig[intensity];

  const createParticle = useCallback((x?: number, y?: number): Particle => {
    const type = types[Math.floor(Math.random() * types.length)];
    const color = colors[Math.floor(Math.random() * colors.length)];
    
    const baseSize = type === 'bubble' ? 8 : type === 'glow' ? 6 : 4;
    const size = baseSize + Math.random() * 4;
    
    const maxLife = variant === 'explosion' ? 60 : variant === 'trail' ? 120 : 200;
    
    return {
      id: Math.random(),
      x: x ?? Math.random() * (containerRef.current?.clientWidth || window.innerWidth),
      y: y ?? Math.random() * (containerRef.current?.clientHeight || window.innerHeight),
      vx: (Math.random() - 0.5) * 2 * config.speed,
      vy: variant === 'floating' ? -Math.random() * 2 * config.speed : (Math.random() - 0.5) * 2 * config.speed,
      size,
      color,
      opacity: Math.random() * 0.8 + 0.2,
      life: maxLife,
      maxLife,
      type,
    };
  }, [colors, types, config.speed, variant]);

  const updateParticles = useCallback(() => {
    setParticles(prevParticles => {
      const updated = prevParticles.map(particle => {
        const newParticle = { ...particle };
        
        // Update position
        newParticle.x += newParticle.vx;
        newParticle.y += newParticle.vy;
        
        // Apply gravity for fire particles
        if (newParticle.type === 'fire') {
          newParticle.vy -= 0.1;
        }
        
        // Apply physics for bubbles
        if (newParticle.type === 'bubble') {
          newParticle.vy -= 0.05;
          newParticle.vx *= 0.99;
        }
        
        // Interactive mode - attract to mouse
        if (interactive && mousePos.x && mousePos.y) {
          const dx = mousePos.x - newParticle.x;
          const dy = mousePos.y - newParticle.y;
          const distance = Math.sqrt(dx * dx + dy * dy);
          
          if (distance < 100) {
            const force = (100 - distance) / 100 * 0.1;
            newParticle.vx += (dx / distance) * force;
            newParticle.vy += (dy / distance) * force;
          }
        }
        
        // Update life
        newParticle.life -= 1;
        newParticle.opacity = (newParticle.life / newParticle.maxLife) * 0.8;
        
        // Boundary checks
        const container = containerRef.current;
        if (container) {
          if (newParticle.x < 0 || newParticle.x > container.clientWidth) {
            newParticle.vx *= -0.8;
          }
          if (newParticle.y < 0 || newParticle.y > container.clientHeight) {
            newParticle.vy *= -0.8;
          }
        }
        
        return newParticle;
      });
      
      // Remove dead particles and add new ones
      const alive = updated.filter(p => p.life > 0);
      const needed = config.count - alive.length;
      
      for (let i = 0; i < needed; i++) {
        alive.push(createParticle());
      }
      
      return alive;
    });
  }, [config.count, createParticle, interactive, mousePos]);

  const handleMouseMove = useCallback((e: React.MouseEvent) => {
    if (!interactive) return;
    
    const rect = containerRef.current?.getBoundingClientRect();
    if (rect) {
      setMousePos({
        x: e.clientX - rect.left,
        y: e.clientY - rect.top,
      });
    }
  }, [interactive]);

  const handleClick = useCallback((e: React.MouseEvent) => {
    if (!interactive) return;
    
    const rect = containerRef.current?.getBoundingClientRect();
    if (rect) {
      const x = e.clientX - rect.left;
      const y = e.clientY - rect.top;
      
      // Create explosion effect
      const explosionParticles = Array.from({ length: 20 }, () => createParticle(x, y));
      setParticles(prev => [...prev, ...explosionParticles]);
    }
  }, [interactive, createParticle]);

  useEffect(() => {
    // Initialize particles
    const initialParticles = Array.from({ length: config.count }, () => createParticle());
    setParticles(initialParticles);
  }, [config.count, createParticle]);

  useEffect(() => {
    const animate = () => {
      updateParticles();
      animationRef.current = requestAnimationFrame(animate);
    };
    
    animationRef.current = requestAnimationFrame(animate);
    
    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
    };
  }, [updateParticles]);

  const getParticleStyle = (particle: Particle) => {
    const baseStyle = {
      position: 'absolute' as const,
      left: particle.x,
      top: particle.y,
      width: particle.size,
      height: particle.size,
      opacity: particle.opacity,
      pointerEvents: 'none' as const,
      borderRadius: '50%',
    };

    switch (particle.type) {
      case 'fire':
        return {
          ...baseStyle,
          background: `radial-gradient(circle, ${particle.color} 0%, transparent 70%)`,
          filter: 'blur(1px)',
        };
      case 'spark':
        return {
          ...baseStyle,
          background: particle.color,
          boxShadow: `0 0 ${particle.size * 2}px ${particle.color}`,
        };
      case 'glow':
        return {
          ...baseStyle,
          background: `radial-gradient(circle, ${particle.color}80 0%, transparent 100%)`,
          filter: 'blur(2px)',
        };
      case 'bubble':
        return {
          ...baseStyle,
          background: `radial-gradient(circle at 30% 30%, ${particle.color}40, ${particle.color}20)`,
          border: `1px solid ${particle.color}60`,
          filter: 'blur(0.5px)',
        };
      default:
        return {
          ...baseStyle,
          background: particle.color,
        };
    }
  };

  return (
    <div
      ref={containerRef}
      className={`fixed inset-0 pointer-events-none z-0 overflow-hidden ${className}`}
      onMouseMove={interactive ? handleMouseMove : undefined}
      onClick={interactive ? handleClick : undefined}
      style={{ pointerEvents: interactive ? 'auto' : 'none' }}
    >
      <AnimatePresence>
        {particles.map((particle) => (
          <motion.div
            key={particle.id}
            style={getParticleStyle(particle)}
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            exit={{ scale: 0, opacity: 0 }}
            transition={{ duration: 0.3 }}
          />
        ))}
      </AnimatePresence>
      
      {/* Interactive cursor effect */}
      {interactive && mousePos.x && mousePos.y && (
        <motion.div
          className="absolute w-8 h-8 border-2 border-orange-500/50 rounded-full pointer-events-none"
          style={{
            left: mousePos.x - 16,
            top: mousePos.y - 16,
          }}
          animate={{
            scale: [1, 1.2, 1],
            opacity: [0.5, 1, 0.5],
          }}
          transition={{
            duration: 1,
            repeat: Infinity,
            ease: 'easeInOut',
          }}
        />
      )}
    </div>
  );
};

export default ParticleSystem;
