/** @type {import('tailwindcss').Config} */
export default {
  darkMode: ["class"],
  content: [
    './pages/**/*.{ts,tsx}',
    './components/**/*.{ts,tsx}',
    './app/**/*.{ts,tsx}',
    './src/**/*.{ts,tsx}',
 ],
  safelist: [
    'border-purple-500/30',
    'bg-purple-500/10',
    'text-purple-400',
    'bg-purple-600',
    'hover:bg-purple-700',
    'border-pink-500/30',
    'bg-pink-500/10',
    'text-pink-400',
    'bg-pink-600',
    'hover:bg-pink-700',
    'border-blue-500/30',
    'bg-blue-500/10',
    'text-blue-400',
    'bg-blue-600',
    'hover:bg-blue-700',
    'border-green-500/30',
    'bg-green-500/10',
    'text-green-400',
    'bg-green-600',
    'hover:bg-green-700',
  ],
  theme: {
    container: {
      center: true,
      padding: "2rem",
      screens: {
        "2xl": "1400px",
      },
    },
    extend: {
      colors: {
        'brand-green': '#14AE5C',
        'brand-orange': '#FF5722',
        'accent-yellow': '#F9D423',
        'sidebar-background': '#121212',
        'card-background': '#1A1A1A',
        'input-background': '#242424',
        'text-primary': '#FFFFFF',
        'text-secondary': '#A3A3A3',
        'card-border': 'rgba(255, 255, 255, 0.1)',
        'input-border': 'rgba(255, 255, 255, 0.2)',
        border: "hsl(var(--border))",
        input: "hsl(var(--input))",
        ring: "hsl(var(--ring))",
        background: "hsl(var(--background))",
        foreground: "hsl(var(--foreground))",
        primary: {
          DEFAULT: "hsl(var(--primary))",
          foreground: "hsl(var(--primary-foreground))",
        },
        secondary: {
          DEFAULT: "hsl(var(--secondary))",
          foreground: "hsl(var(--secondary-foreground))",
        },
        destructive: {
          DEFAULT: "hsl(var(--destructive))",
          foreground: "hsl(var(--destructive-foreground))",
        },
        muted: {
          DEFAULT: "hsl(var(--muted))",
          foreground: "hsl(var(--muted-foreground))",
        },
        accent: {
          DEFAULT: "hsl(var(--accent))",
          foreground: "hsl(var(--accent-foreground))",
        },
        popover: {
          DEFAULT: "hsl(var(--popover))",
          foreground: "hsl(var(--popover-foreground))",
        },
        card: {
          DEFAULT: "hsl(var(--card))",
          foreground: "hsl(var(--card-foreground))",
        },
      },
      borderRadius: {
        lg: "12px",
        md: "8px",
        sm: "4px",
      },
      boxShadow: {
        'elevation-1': '0 1px 3px rgba(0,0,0,.12)',
        'elevation-2': '0 4px 6px rgba(0,0,0,.12)',
      },
      keyframes: {
        "accordion-down": {
          from: { height: 0 },
          to: { height: "var(--radix-accordion-content-height)" },
        },
        "accordion-up": {
          from: { height: "var(--radix-accordion-content-height)" },
          to: { height: 0 },
        },
        "shimmer-ui": {
          "0%, 90%, 100%": {
            "background-position": "calc(-100% - var(--shimmer-width)) 0",
          },
          "30%, 60%": {
            "background-position": "calc(100% + var(--shimmer-width)) 0",
          },
        },
      },
      animation: {
        "accordion-down": "accordion-down 0.2s ease-out",
        "accordion-up": "accordion-up 0.2s ease-out",
        "shimmer-ui": "shimmer-ui 8s infinite",
      },
    },
  },
  plugins: [require("tailwindcss-animate")],
}
