/** @type {import('tailwindcss').Config} */
export default {
  darkMode: ["class"],
  content: [
    './pages/**/*.{ts,tsx}',
    './components/**/*.{ts,tsx}',
    './app/**/*.{ts,tsx}',
    './src/**/*.{ts,tsx}',
 ],
  safelist: [
    'border-purple-500/30',
    'bg-purple-500/10',
    'text-purple-400',
    'bg-purple-600',
    'hover:bg-purple-700',
    'border-pink-500/30',
    'bg-pink-500/10',
    'text-pink-400',
    'bg-pink-600',
    'hover:bg-pink-700',
    'border-blue-500/30',
    'bg-blue-500/10',
    'text-blue-400',
    'bg-blue-600',
    'hover:bg-blue-700',
    'border-green-500/30',
    'bg-green-500/10',
    'text-green-400',
    'bg-green-600',
    'hover:bg-green-700',
  ],
  theme: {
    container: {
      center: true,
      padding: "2rem",
      screens: {
        "2xl": "1400px",
      },
    },
    extend: {
      colors: {
        'brand-green': '#14AE5C',
        'brand-orange': '#FF5722',
        'accent-yellow': '#F9D423',
        'sidebar-background': '#121212',
        'card-background': '#1A1A1A',
        'input-background': '#242424',
        'text-primary': '#FFFFFF',
        'text-secondary': '#A3A3A3',
        'card-border': 'rgba(255, 255, 255, 0.1)',
        'input-border': 'rgba(255, 255, 255, 0.2)',
        border: "hsl(var(--border))",
        input: "hsl(var(--input))",
        ring: "hsl(var(--ring))",
        background: "hsl(var(--background))",
        foreground: "hsl(var(--foreground))",
        primary: {
          DEFAULT: "hsl(var(--primary))",
          foreground: "hsl(var(--primary-foreground))",
        },
        secondary: {
          DEFAULT: "hsl(var(--secondary))",
          foreground: "hsl(var(--secondary-foreground))",
        },
        destructive: {
          DEFAULT: "hsl(var(--destructive))",
          foreground: "hsl(var(--destructive-foreground))",
        },
        muted: {
          DEFAULT: "hsl(var(--muted))",
          foreground: "hsl(var(--muted-foreground))",
        },
        accent: {
          DEFAULT: "hsl(var(--accent))",
          foreground: "hsl(var(--accent-foreground))",
        },
        popover: {
          DEFAULT: "hsl(var(--popover))",
          foreground: "hsl(var(--popover-foreground))",
        },
        card: {
          DEFAULT: "hsl(var(--card))",
          foreground: "hsl(var(--card-foreground))",
        },
      },
      borderRadius: {
        lg: "12px",
        md: "8px",
        sm: "4px",
      },
      boxShadow: {
        'elevation-1': '0 1px 3px rgba(0,0,0,.12)',
        'elevation-2': '0 4px 6px rgba(0,0,0,.12)',
      },
      keyframes: {
        "accordion-down": {
          from: { height: 0 },
          to: { height: "var(--radix-accordion-content-height)" },
        },
        "accordion-up": {
          from: { height: "var(--radix-accordion-content-height)" },
          to: { height: 0 },
        },
        "shimmer-ui": {
          "0%, 90%, 100%": {
            "background-position": "calc(-100% - var(--shimmer-width)) 0",
          },
          "30%, 60%": {
            "background-position": "calc(100% + var(--shimmer-width)) 0",
          },
        },
        "float": {
          "0%, 100%": {
            transform: "translateY(0px) rotateX(0deg)",
          },
          "50%": {
            transform: "translateY(-10px) rotateX(2deg)",
          },
        },
        "pulse-glow": {
          "0%, 100%": {
            boxShadow: "0 0 20px rgba(255, 61, 0, 0.3)",
          },
          "50%": {
            boxShadow: "0 0 40px rgba(255, 61, 0, 0.6)",
          },
        },
        "shimmer-move": {
          "0%": {
            transform: "translateX(-100%)",
          },
          "100%": {
            transform: "translateX(100%)",
          },
        },
        "rotate-glow": {
          "0%": {
            transform: "rotate(0deg)",
            filter: "hue-rotate(0deg)",
          },
          "100%": {
            transform: "rotate(360deg)",
            filter: "hue-rotate(360deg)",
          },
        },
        "morph-background": {
          "0%, 100%": {
            borderRadius: "20px",
            background: "linear-gradient(45deg, rgba(255,61,0,0.1), rgba(255,87,34,0.1))",
          },
          "25%": {
            borderRadius: "30px 10px 30px 10px",
            background: "linear-gradient(135deg, rgba(147,51,234,0.1), rgba(236,72,153,0.1))",
          },
          "50%": {
            borderRadius: "10px 30px 10px 30px",
            background: "linear-gradient(225deg, rgba(6,182,212,0.1), rgba(59,130,246,0.1))",
          },
          "75%": {
            borderRadius: "25px 15px 25px 15px",
            background: "linear-gradient(315deg, rgba(34,197,94,0.1), rgba(16,185,129,0.1))",
          },
        },
        "particle-float": {
          "0%": {
            transform: "translateY(100vh) translateX(0px) rotate(0deg)",
            opacity: "0",
          },
          "10%": {
            opacity: "1",
          },
          "90%": {
            opacity: "1",
          },
          "100%": {
            transform: "translateY(-100px) translateX(100px) rotate(360deg)",
            opacity: "0",
          },
        },
        "holographic-shift": {
          "0%, 100%": {
            backgroundPosition: "0% 50%",
          },
          "50%": {
            backgroundPosition: "100% 50%",
          },
        },
        "liquid-move": {
          "0%, 100%": {
            transform: "translateX(0%) rotateZ(0deg)",
          },
          "25%": {
            transform: "translateX(5%) rotateZ(1deg)",
          },
          "50%": {
            transform: "translateX(-5%) rotateZ(-1deg)",
          },
          "75%": {
            transform: "translateX(3%) rotateZ(0.5deg)",
          },
        },
      },
      animation: {
        "accordion-down": "accordion-down 0.2s ease-out",
        "accordion-up": "accordion-up 0.2s ease-out",
        "shimmer-ui": "shimmer-ui 8s infinite",
        "float": "float 3s ease-in-out infinite",
        "pulse-glow": "pulse-glow 2s ease-in-out infinite",
        "shimmer-move": "shimmer-move 2s infinite",
        "rotate-glow": "rotate-glow 3s linear infinite",
        "morph-background": "morph-background 8s ease-in-out infinite",
        "particle-float": "particle-float 15s linear infinite",
        "holographic-shift": "holographic-shift 3s ease-in-out infinite",
        "liquid-move": "liquid-move 4s ease-in-out infinite",
      },
    },
  },
  plugins: [require("tailwindcss-animate")],
}
