import React, { useEffect, useRef, useState } from 'react';
import { motion } from 'framer-motion';

interface Particle {
  id: number;
  x: number;
  y: number;
  vx: number;
  vy: number;
  size: number;
  color: string;
  opacity: number;
  life: number;
}

interface InteractiveParticlesProps {
  count?: number;
  colors?: string[];
  size?: 'sm' | 'md' | 'lg';
  speed?: 'slow' | 'normal' | 'fast';
  interactive?: boolean;
  className?: string;
}

export const InteractiveParticles: React.FC<InteractiveParticlesProps> = ({
  count = 50,
  colors = ['#3b82f6', '#8b5cf6', '#f59e0b', '#10b981', '#ef4444'],
  size = 'md',
  speed = 'normal',
  interactive = true,
  className = ''
}) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const [particles, setParticles] = useState<Particle[]>([]);
  const [mousePos, setMousePos] = useState({ x: 0, y: 0 });
  const animationRef = useRef<number>();

  const sizeMap = {
    sm: { min: 1, max: 3 },
    md: { min: 2, max: 5 },
    lg: { min: 3, max: 8 }
  };

  const speedMap = {
    slow: 0.5,
    normal: 1,
    fast: 2
  };

  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    const resizeCanvas = () => {
      canvas.width = canvas.offsetWidth;
      canvas.height = canvas.offsetHeight;
    };

    resizeCanvas();
    window.addEventListener('resize', resizeCanvas);

    // Initialize particles
    const initialParticles: Particle[] = [];
    for (let i = 0; i < count; i++) {
      initialParticles.push({
        id: i,
        x: Math.random() * canvas.width,
        y: Math.random() * canvas.height,
        vx: (Math.random() - 0.5) * speedMap[speed],
        vy: (Math.random() - 0.5) * speedMap[speed],
        size: Math.random() * (sizeMap[size].max - sizeMap[size].min) + sizeMap[size].min,
        color: colors[Math.floor(Math.random() * colors.length)],
        opacity: Math.random() * 0.8 + 0.2,
        life: Math.random() * 100 + 50
      });
    }
    setParticles(initialParticles);

    const handleMouseMove = (e: MouseEvent) => {
      const rect = canvas.getBoundingClientRect();
      setMousePos({
        x: e.clientX - rect.left,
        y: e.clientY - rect.top
      });
    };

    if (interactive) {
      canvas.addEventListener('mousemove', handleMouseMove);
    }

    return () => {
      window.removeEventListener('resize', resizeCanvas);
      if (interactive) {
        canvas.removeEventListener('mousemove', handleMouseMove);
      }
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
    };
  }, [count, colors, size, speed, interactive]);

  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    const animate = () => {
      ctx.clearRect(0, 0, canvas.width, canvas.height);

      setParticles(prevParticles => {
        return prevParticles.map(particle => {
          let newParticle = { ...particle };

          // Mouse interaction
          if (interactive) {
            const dx = mousePos.x - newParticle.x;
            const dy = mousePos.y - newParticle.y;
            const distance = Math.sqrt(dx * dx + dy * dy);
            
            if (distance < 100) {
              const force = (100 - distance) / 100;
              newParticle.vx += (dx / distance) * force * 0.1;
              newParticle.vy += (dy / distance) * force * 0.1;
            }
          }

          // Update position
          newParticle.x += newParticle.vx;
          newParticle.y += newParticle.vy;

          // Boundary collision
          if (newParticle.x < 0 || newParticle.x > canvas.width) {
            newParticle.vx *= -0.8;
            newParticle.x = Math.max(0, Math.min(canvas.width, newParticle.x));
          }
          if (newParticle.y < 0 || newParticle.y > canvas.height) {
            newParticle.vy *= -0.8;
            newParticle.y = Math.max(0, Math.min(canvas.height, newParticle.y));
          }

          // Friction
          newParticle.vx *= 0.99;
          newParticle.vy *= 0.99;

          // Life cycle
          newParticle.life -= 0.5;
          if (newParticle.life <= 0) {
            newParticle.x = Math.random() * canvas.width;
            newParticle.y = Math.random() * canvas.height;
            newParticle.vx = (Math.random() - 0.5) * speedMap[speed];
            newParticle.vy = (Math.random() - 0.5) * speedMap[speed];
            newParticle.life = Math.random() * 100 + 50;
            newParticle.color = colors[Math.floor(Math.random() * colors.length)];
          }

          // Draw particle
          ctx.save();
          ctx.globalAlpha = newParticle.opacity * (newParticle.life / 100);
          ctx.fillStyle = newParticle.color;
          ctx.shadowBlur = newParticle.size * 2;
          ctx.shadowColor = newParticle.color;
          
          ctx.beginPath();
          ctx.arc(newParticle.x, newParticle.y, newParticle.size, 0, Math.PI * 2);
          ctx.fill();
          
          // Add glow effect
          ctx.globalAlpha = newParticle.opacity * 0.3;
          ctx.beginPath();
          ctx.arc(newParticle.x, newParticle.y, newParticle.size * 2, 0, Math.PI * 2);
          ctx.fill();
          
          ctx.restore();

          return newParticle;
        });
      });

      // Draw connections between nearby particles
      ctx.strokeStyle = 'rgba(255, 255, 255, 0.1)';
      ctx.lineWidth = 1;
      
      particles.forEach((particle, i) => {
        particles.slice(i + 1).forEach(otherParticle => {
          const dx = particle.x - otherParticle.x;
          const dy = particle.y - otherParticle.y;
          const distance = Math.sqrt(dx * dx + dy * dy);
          
          if (distance < 80) {
            ctx.globalAlpha = (80 - distance) / 80 * 0.3;
            ctx.beginPath();
            ctx.moveTo(particle.x, particle.y);
            ctx.lineTo(otherParticle.x, otherParticle.y);
            ctx.stroke();
          }
        });
      });

      animationRef.current = requestAnimationFrame(animate);
    };

    animate();

    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
    };
  }, [particles, mousePos, interactive, colors, speed]);

  return (
    <motion.canvas
      ref={canvasRef}
      className={`absolute inset-0 pointer-events-none ${className}`}
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 2 }}
      style={{ 
        width: '100%', 
        height: '100%',
        background: 'transparent'
      }}
    />
  );
};