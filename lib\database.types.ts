export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: J<PERSON> | undefined }
  | Json[]

export interface Database {
  public: {
    Tables: {
      users: {
        Row: {
          id: string
          email: string
          username: string | null
          full_name: string | null
          avatar_url: string | null
          gender: string | null
          created_at: string
          updated_at: string
          last_login: string | null
          streak_count: number
          total_generations: number
          subscription_tier: 'free' | 'premium' | 'pro'
        }
        Insert: {
          id: string
          email: string
          username?: string | null
          full_name?: string | null
          avatar_url?: string | null
          gender?: string | null
          created_at?: string
          updated_at?: string
          last_login?: string | null
          streak_count?: number
          total_generations?: number
          subscription_tier?: 'free' | 'premium' | 'pro'
        }
        Update: {
          id?: string
          email?: string
          username?: string | null
          full_name?: string | null
          avatar_url?: string | null
          gender?: string | null
          created_at?: string
          updated_at?: string
          last_login?: string | null
          streak_count?: number
          total_generations?: number
          subscription_tier?: 'free' | 'premium' | 'pro'
        }
      }
      generations: {
        Row: {
          id: string
          user_id: string
          type: 'compliment' | 'date_idea' | 'story' | 'topic' | 'chat'
          content: string
          platform: string | null
          tone: string | null
          metadata: Json | null
          created_at: string
          updated_at: string
          is_favorite: boolean
          usage_count: number
        }
        Insert: {
          id?: string
          user_id: string
          type: 'compliment' | 'date_idea' | 'story' | 'topic' | 'chat'
          content: string
          platform?: string | null
          tone?: string | null
          metadata?: Json | null
          created_at?: string
          updated_at?: string
          is_favorite?: boolean
          usage_count?: number
        }
        Update: {
          id?: string
          user_id?: string
          type?: 'compliment' | 'date_idea' | 'story' | 'topic' | 'chat'
          content?: string
          platform?: string | null
          tone?: string | null
          metadata?: Json | null
          created_at?: string
          updated_at?: string
          is_favorite?: boolean
          usage_count?: number
        }
      }
      conversations: {
        Row: {
          id: string
          user_id: string
          title: string | null
          messages: Json
          created_at: string
          updated_at: string
          is_archived: boolean
        }
        Insert: {
          id?: string
          user_id: string
          title?: string | null
          messages?: Json
          created_at?: string
          updated_at?: string
          is_archived?: boolean
        }
        Update: {
          id?: string
          user_id?: string
          title?: string | null
          messages?: Json
          created_at?: string
          updated_at?: string
          is_archived?: boolean
        }
      }
      user_preferences: {
        Row: {
          id: string
          user_id: string
          preferred_platforms: string[]
          preferred_tones: string[]
          language: string
          notifications_enabled: boolean
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          preferred_platforms?: string[]
          preferred_tones?: string[]
          language?: string
          notifications_enabled?: boolean
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          preferred_platforms?: string[]
          preferred_tones?: string[]
          language?: string
          notifications_enabled?: boolean
          created_at?: string
          updated_at?: string
        }
      }
      analytics_events: {
        Row: {
          id: string
          user_id: string | null
          event_type: string
          event_data: Json
          created_at: string
        }
        Insert: {
          id?: string
          user_id?: string | null
          event_type: string
          event_data?: Json
          created_at?: string
        }
        Update: {
          id?: string
          user_id?: string | null
          event_type?: string
          event_data?: Json
          created_at?: string
        }
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      [_ in never]: never
    }
  }
}
