import React, { useState, useRef, useEffect } from 'react';
import { motion } from 'framer-motion';

interface GlowingEffectProps {
  children: React.ReactNode;
  color?: 'orange' | 'blue' | 'purple' | 'green' | 'pink' | 'cyan';
  intensity?: 'low' | 'medium' | 'high' | 'extreme';
  size?: 'sm' | 'md' | 'lg' | 'xl';
  animated?: boolean;
  className?: string;
  pulsing?: boolean;
  floating?: boolean;
}

export const GlowingEffect: React.FC<GlowingEffectProps> = ({
  children,
  color = 'orange',
  intensity = 'medium',
  size = 'md',
  animated = true,
  className = '',
  pulsing = true
}) => {
  const [isHovered, setIsHovered] = useState(false);
  const containerRef = useRef<HTMLDivElement>(null);

  const colors = {
    orange: {
      glow: 'shadow-orange-500/50',
      border: 'border-orange-400/30',
      bg: 'bg-orange-500/10',
      light: 'rgba(251, 146, 60, 0.5)'
    },
    blue: {
      glow: 'shadow-blue-500/50',
      border: 'border-blue-400/30',
      bg: 'bg-blue-500/10',
      light: 'rgba(59, 130, 246, 0.5)'
    },
    purple: {
      glow: 'shadow-purple-500/50',
      border: 'border-purple-400/30',
      bg: 'bg-purple-500/10',
      light: 'rgba(168, 85, 247, 0.5)'
    },
    green: {
      glow: 'shadow-green-500/50',
      border: 'border-green-400/30',
      bg: 'bg-green-500/10',
      light: 'rgba(34, 197, 94, 0.5)'
    },
    pink: {
      glow: 'shadow-pink-500/50',
      border: 'border-pink-400/30',
      bg: 'bg-pink-500/10',
      light: 'rgba(236, 72, 153, 0.5)'
    },
    cyan: {
      glow: 'shadow-cyan-500/50',
      border: 'border-cyan-400/30',
      bg: 'bg-cyan-500/10',
      light: 'rgba(34, 211, 238, 0.5)'
    }
  };

  const selectedColor = colors[color] || colors.orange;

  const intensities = {
    low: 'shadow-lg',
    medium: 'shadow-xl',
    high: 'shadow-2xl',
    extreme: 'shadow-[0_0_50px_rgba(0,0,0,0.5)]'
  };

  const sizes = {
    sm: 'p-2',
    md: 'p-4',
    lg: 'p-6',
    xl: 'p-8'
  };


  return (
    <motion.div
      ref={containerRef}
      className={`
        relative
        ${sizes[size]}
        ${selectedColor.bg}
        ${selectedColor.border}
        ${selectedColor.glow}
        ${intensities[intensity]}
        border
        rounded-2xl
        backdrop-blur-sm
        transition-all duration-300
        transform-gpu
        ${className}
      `}
      style={{
        filter: `drop-shadow(0 0 20px ${selectedColor.light})`,
      }}
      initial={{ opacity: 0, scale: 0.9 }}
      animate={{ opacity: 1, scale: 1 }}
      transition={{ duration: 0.5, ease: "easeOut" }}
      whileHover={{
        scale: 1.05
      }}
      onHoverStart={() => setIsHovered(true)}
      onHoverEnd={() => setIsHovered(false)}
    >
      {/* Main glow effect */}
      <motion.div
        className={`absolute inset-0 rounded-2xl ${selectedColor.glow} blur-xl`}
        animate={{
          opacity: pulsing ? [0.3, 0.8, 0.3] : 0.5,
          scale: isHovered ? 1.1 : 1
        }}
        transition={{
          opacity: {
            duration: 2,
            repeat: pulsing ? Infinity : 0,
            ease: "easeInOut"
          },
          scale: {
            duration: 0.3
          }
        }}
      />

      {/* Secondary glow layer */}
      <motion.div
        className={`absolute inset-0 rounded-2xl ${selectedColor.glow} blur-2xl opacity-30`}
        animate={{
          scale: pulsing ? [0.8, 1.2, 0.8] : 1
        }}
        transition={{
          duration: 3,
          repeat: pulsing ? Infinity : 0,
          ease: "easeInOut"
        }}
      />

      {/* Animated border */}
      <motion.div
        className="absolute inset-0 rounded-2xl"
        style={{
          background: `
            linear-gradient(45deg, 
              transparent 30%, 
              ${selectedColor.light} 50%,
              transparent 70%
            )
          `,
          backgroundSize: '200% 200%'
        }}
        animate={{
          backgroundPosition: isHovered && animated ? ['0% 0%', '100% 100%'] : '0% 0%'
        }}
        transition={{
          duration: 2,
          repeat: isHovered && animated ? Infinity : 0,
          ease: "linear"
        }}
      />

      {/* Floating particles */}
      {animated && isHovered && (
        <div className="absolute inset-0 pointer-events-none overflow-hidden rounded-2xl">
          {[...Array(12)].map((_, i) => (
            <motion.div
              key={i}
              className={`absolute w-1 h-1 ${selectedColor.bg} rounded-full`}
              style={{
                left: `${10 + (i * 8)}%`,
                top: `${20 + (i * 6)}%`,
                filter: `drop-shadow(0 0 4px ${selectedColor.light})`
              }}
              animate={{
                y: [-10, 10, -10],
                x: [-5, 5, -5],
                opacity: [0.3, 1, 0.3],
                scale: [0.5, 1.5, 0.5]
              }}
              transition={{
                duration: 3 + i * 0.2,
                repeat: Infinity,
                ease: "easeInOut"
              }}
            />
          ))}
        </div>
      )}

      {/* Inner highlight */}
      <motion.div
        className="absolute inset-1 rounded-xl bg-white/5"
        animate={{
          opacity: isHovered ? 0.1 : 0.05
        }}
        transition={{ duration: 0.3 }}
      />

      {/* Content */}
      <div className="relative z-10">
        {children}
      </div>
    </motion.div>
  );
};

export default GlowingEffect;