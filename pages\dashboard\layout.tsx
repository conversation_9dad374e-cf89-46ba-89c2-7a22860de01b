
import React from 'react';
import { Settings, User as UserIcon, LogOut, Bell, Star, MessageSquare, Bot, Image as ImageIcon } from 'lucide-react';
import { PapoFireLogo, ComplimentIcon, StoryAnalysisIcon, ChatAnalysisIcon, DateIdeaIcon, RouletteIcon } from '../../components/ui/icons';
import { User, Page } from '../../types';

const NavItem: React.FC<{ icon: React.ReactNode; label: string; active?: boolean; onClick: () => void }> = ({ icon, label, active, onClick }) => (
  <button onClick={onClick} className={`flex items-center gap-3 px-4 py-3 rounded-lg transition-colors ${active ? 'bg-brand-orange/10 text-brand-orange' : 'text-text-secondary hover:bg-white/5'}`}>
    {icon}
    <span className="font-semibold">{label}</span>
  </button>
);

export const DashboardLayout: React.FC<{ user: User; onLogout: () => void; onNavigate: (page: Page) => void; children: React.ReactNode; activePage: Page }> = ({ user, onLogout, onNavigate, children, activePage }) => {
  return (
    <div className="min-h-screen w-full bg-background text-text-primary flex">
      {/* Sidebar */}
      <aside className="w-64 bg-background-secondary p-6 flex-col hidden md:flex">
        <div className="mb-12 flex items-center gap-3">
          <PapoFireLogo className="w-10 h-10 text-orange-500" />
          <span className="text-xl font-bold bg-gradient-to-r from-orange-400 to-red-500 bg-clip-text text-transparent">PapoFire</span>
        </div>
        <nav className="flex flex-col gap-2">
          <NavItem icon={<MessageSquare size={20} />} label="Dashboard" active={activePage === Page.Dashboard} onClick={() => onNavigate(Page.Dashboard)} />
          <NavItem icon={<ComplimentIcon className="w-5 h-5" />} label="Elogios" active={activePage === Page.GeneratorCompliment} onClick={() => onNavigate(Page.GeneratorCompliment)} />
          <NavItem icon={<StoryAnalysisIcon className="w-5 h-5" />} label="Análise de Story" active={activePage === Page.GeneratorStory} onClick={() => onNavigate(Page.GeneratorStory)} />
          <NavItem icon={<ChatAnalysisIcon className="w-5 h-5" />} label="Análise de Chat" active={activePage === Page.GeneratorChat} onClick={() => onNavigate(Page.GeneratorChat)} />
          <NavItem icon={<DateIdeaIcon className="w-5 h-5" />} label="Ideias de Encontro" active={activePage === Page.GeneratorDateIdea} onClick={() => onNavigate(Page.GeneratorDateIdea)} />
          <NavItem icon={<RouletteIcon className="w-5 h-5" />} label="Roleta de Temas" active={activePage === Page.Roleta} onClick={() => onNavigate(Page.Roleta)} />
          <div className="border-t border-white/10 my-4"></div>
          <NavItem icon={<Star size={20} />} label="Upgrade PRO" active={activePage === Page.Upgrade} onClick={() => onNavigate(Page.Upgrade)} />
        </nav>
        <div className="mt-auto">
          <NavItem icon={<LogOut size={20} />} label="Sair" onClick={onLogout} />
        </div>
      </aside>

      {/* Main Content */}
      <div className="flex-1 flex flex-col">
        {/* Header */}
        <header className="flex items-center justify-between p-6 border-b border-border">
          <h1 className="text-xl font-bold">Como posso te ajudar hoje?</h1>
          <div className="flex items-center gap-4">
            <button className="relative p-2 rounded-full hover:bg-white/5">
              <Bell size={20} />
              <span className="absolute top-1 right-1 w-2 h-2 bg-brand-purple rounded-full"></span>
            </button>
            <div className="w-px h-6 bg-border"></div>
            <div className="flex items-center gap-3">
              <div className="w-8 h-8 rounded-full bg-gradient-to-r from-orange-400 to-red-500 flex items-center justify-center">
                <UserIcon size={16} className="text-white" />
              </div>
              <div>
                <p className="font-semibold text-sm">{user.email}</p>
                <p className="text-xs text-text-secondary">Plano Free</p>
              </div>
            </div>
            <button className="p-2 rounded-full hover:bg-white/5">
              <Settings size={20} />
            </button>
          </div>
        </header>

        {/* Page Content */}
        <main className="flex-1 p-6 overflow-y-auto">
          {children}
        </main>
      </div>
    </div>
  );
};
