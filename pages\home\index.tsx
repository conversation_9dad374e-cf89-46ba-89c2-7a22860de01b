import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Flame, Sparkles, MessageCircle, Users, Zap, Heart, Star, Trophy, BrainCircuit, Bot, Wand2 } from 'lucide-react';
import { Page } from '../../types';
import { GlassCard } from '../../components/glass-card';
import { LavaLamp as FluidBlob } from '../../components/fluid-blob';
import { LiquidButton } from '../../components/liquid-glass-button';
import { GradientButton } from '../../components/gradient-button';
import { GlowingEffect } from '../../components/glowing-effect';
import { VapourTextEffect } from '../../components/vapour-text-effect';

interface HomePageProps {
  onNavigate: (page: Page) => void;
}

const HomePage: React.FC<HomePageProps> = ({ onNavigate }) => {
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });
  const [isLoaded, setIsLoaded] = useState(false);

  useEffect(() => {
    setIsLoaded(true);
    
    const handleMouseMove = (e: MouseEvent) => {
      setMousePosition({ x: e.clientX, y: e.clientY });
    };

    window.addEventListener('mousemove', handleMouseMove);
    return () => window.removeEventListener('mousemove', handleMouseMove);
  }, []);

  const features = [
    {
      icon: MessageCircle,
      title: "Chat Inteligente",
      description: "Converse com nossa IA avançada sobre qualquer assunto",
      color: "fire" as const,
      page: "chat" as Page
    },
    {
      icon: Wand2,
      title: "Roleta de Temas",
      description: "Descubra novos tópicos de conversa aleatoriamente",
      color: "cosmic" as const,
      page: "roleta" as Page
    },
    {
      icon: Bot,
      title: "Histórias Criativas",
      description: "Crie e explore narrativas únicas com IA",
      color: "electric" as const,
      page: "stories" as Page
    },
    {
      icon: Users,
      title: "Dashboard",
      description: "Gerencie suas conversas e configurações",
      color: "nature" as const,
      page: "dashboard" as Page
    }
  ];

  const stats = [
    { number: "1M+", label: "Conversas Geradas", color: "fire" as const },
    { number: "50K+", label: "Usuários Ativos", color: "cosmic" as const },
    { number: "99.9%", label: "Uptime", color: "electric" as const },
    { number: "24/7", label: "Suporte IA", color: "nature" as const }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-black to-gray-800 relative overflow-hidden">
      {/* Animated background with 4D effects */}
      <div className="absolute inset-0 overflow-hidden">
        
        {/* Floating particles */}
        <div className="absolute inset-0 pointer-events-none">
          {[...Array(25)].map((_, i) => (
            <motion.div
              key={i}
              className="absolute w-1 h-1 bg-orange-400/30 rounded-full"
              style={{
                left: `${Math.random() * 100}%`,
                top: `${Math.random() * 100}%`
              }}
              animate={{
                y: [-15, 15, -15],
                x: [-8, 8, -8],
                opacity: [0.3, 0.8, 0.3],
                scale: [0.5, 1.2, 0.5]
              }}
              transition={{
                duration: 3 + i * 0.2,
                repeat: Infinity,
                ease: "easeInOut"
              }}
            />
          ))}
        </div>
      </div>

      {/* Mouse follower effect */}
      <motion.div
        className="fixed w-80 h-80 rounded-full bg-orange-500/10 blur-3xl pointer-events-none z-0"
        style={{
          left: mousePosition.x - 160,
          top: mousePosition.y - 160
        }}
        animate={{
          scale: [1, 1.3, 1],
          opacity: [0.1, 0.4, 0.1]
        }}
        transition={{
          duration: 2.5,
          repeat: Infinity,
          ease: "easeInOut"
        }}
      />

      <div className="relative z-10 container mx-auto px-6 py-12">
        {/* Header */}
        <motion.header
          className="flex justify-between items-center mb-16"
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: isLoaded ? 1 : 0, y: isLoaded ? 0 : -20 }}
          transition={{ duration: 0.8 }}
        >
          <GlowingEffect color="orange" size="sm" className="flex items-center gap-3">
            <Flame className="w-8 h-8 text-orange-400" />
            <span className="text-4xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-orange-400 to-pink-500">
              PapoFire
            </span>
          </GlowingEffect>

          <div className="flex gap-4">
            <LiquidButton 
              variant="secondary" 
              size="lg"
              onClick={() => onNavigate('dashboard')}
            >
              <Users className="w-5 h-5" />
              Dashboard
            </LiquidButton>
          </div>
        </motion.header>

        {/* Hero Section */}
        <motion.section
          className="text-center mb-20"
          initial={{ opacity: 0, scale: 0.9, rotateX: -20 }}
          animate={{ opacity: isLoaded ? 1 : 0, scale: isLoaded ? 1 : 0.9, rotateX: isLoaded ? 0 : -20 }}
          transition={{ duration: 1, delay: 0.2 }}
          style={{ transformStyle: 'preserve-3d' }}
        >
          <GlowingEffect 
            color="orange" 
            size="xl" 
            className="mb-8 inline-block"
            pulsing={true}
            floating={true}
          >
            <span className="text-5xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-orange-400 to-pink-500">
              Bem-vindo ao PapoFire
            </span>
          </GlowingEffect>

          <motion.div
            className="mb-8"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.5 }}
          >
            <p className="text-xl text-gray-300 max-w-3xl mx-auto leading-relaxed">
              Explore o poder da inteligência artificial conversacional. 
              Escolha uma das opções abaixo para começar sua jornada.
            </p>
          </motion.div>

          <motion.div
            className="flex flex-col sm:flex-row gap-6 justify-center items-center"
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.7 }}
          >
            <GradientButton
              variant="fire"
              size="xl"
              onClick={() => onNavigate('chat')}
              className="min-w-[200px]"
            >
              <MessageCircle className="w-6 h-6" />
              Iniciar Chat
            </GradientButton>

            <GradientButton
              variant="cosmic"
              size="xl"
              onClick={() => onNavigate('roleta')}
              className="min-w-[200px]"
            >
              <Wand2 className="w-6 h-6" />
              Roleta de Temas
            </GradientButton>
          </motion.div>
        </motion.section>

        {/* Features Grid */}
        <motion.section
          className="mb-20"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 1, delay: 0.9 }}
        >
          <div className="text-center mb-12">
            <span className="text-4xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-purple-400 to-pink-500">
              Explore Todas as Funcionalidades
            </span>
            <p className="text-lg text-gray-400">
              Descubra tudo o que o PapoFire pode fazer por você
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {features.map((feature, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 50, rotateX: -30 }}
                animate={{ opacity: 1, y: 0, rotateX: 0 }}
                transition={{ 
                  duration: 0.8, 
                  delay: 1.1 + index * 0.1,
                  ease: "easeOut"
                }}
                whileHover={{ 
                  scale: 1.05,
                  rotateY: 10,
                  rotateX: 5
                }}
                style={{ transformStyle: 'preserve-3d' }}
                onClick={() => onNavigate(feature.page)}
                className="cursor-pointer"
              >
                <GlassCard
                  variant="holographic"
                  size="lg"
                  glow={true}
                  floating={true}
                  className="h-full text-center"
                >
                  <GlowingEffect 
                    color={feature.color === 'fire' ? 'orange' : 
                           feature.color === 'cosmic' ? 'purple' :
                           feature.color === 'electric' ? 'cyan' :
                           feature.color === 'nature' ? 'green' : 'blue'}
                    size="sm"
                    className="mb-4 inline-block"
                  >
                    <feature.icon className="w-8 h-8" />
                  </GlowingEffect>
                  
                  <span className="text-2xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-orange-400 to-pink-500">
                    {feature.title}
                  </span>
                  
                  <p className="text-gray-300 leading-relaxed text-sm">
                    {feature.description}
                  </p>
                </GlassCard>
              </motion.div>
            ))}
          </div>
        </motion.section>

        {/* Stats Section */}
        <motion.section
          className="mb-20"
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 1, delay: 1.3 }}
        >
          <div className="text-center mb-12">
            <span className="text-4xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-orange-400 to-pink-500">
              Números que Impressionam
            </span>
          </div>

          <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
            {stats.map((stat, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 30 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ 
                  duration: 0.8, 
                  delay: 1.5 + index * 0.1
                }}
                whileHover={{ 
                  scale: 1.1,
                  rotateY: 5
                }}
              >
                <GlassCard
                  variant="glass"
                  size="md"
                  glow={true}
                  floating={true}
                  className="text-center"
                >
                  <GlowingEffect 
                    color={stat.color === 'fire' ? 'orange' : 
                           stat.color === 'cosmic' ? 'purple' :
                           stat.color === 'electric' ? 'cyan' : 'green'}
                    size="sm"
                    floating={true}
                  >
                    <span className="text-5xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-orange-400 to-pink-500">
                      {stat.number}
                    </span>
                  </GlowingEffect>
                  
                  <p className="text-gray-400 text-sm">{stat.label}</p>
                </GlassCard>
              </motion.div>
            ))}
          </div>
        </motion.section>

        {/* CTA Section */}
        <motion.section
          className="text-center"
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 1, delay: 1.7 }}
        >
          <GlassCard
            variant="holographic"
            size="xl"
            glow={true}
            floating={true}
            className="max-w-4xl mx-auto"
          >
            <span className="text-5xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-orange-400 to-pink-500">
              Pronto para Começar?
            </span>
            
            <p className="text-lg text-gray-300 mb-8 max-w-2xl mx-auto">
              Escolha sua aventura e descubra o poder da IA conversacional mais avançada do Brasil.
            </p>

            <div className="flex flex-col sm:flex-row gap-6 justify-center">
              <GradientButton
                variant="fire"
                size="xl"
                onClick={() => onNavigate('chat')}
                className="min-w-[250px]"
              >
                <MessageCircle className="w-6 h-6" />
                Começar a Conversar
              </GradientButton>

              <LiquidButton 
                variant="secondary" 
                size="xl"
                onClick={() => onNavigate('stories')}
                className="min-w-[250px]"
              >
                <Bot className="w-6 h-6" />
                Explorar Histórias
              </LiquidButton>
            </div>
          </GlassCard>
        </motion.section>
      </div>
    </div>
  );
};

export default HomePage;
