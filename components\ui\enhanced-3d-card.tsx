import React, { useState, useRef, useCallback } from 'react';
import { motion, useMotionValue, useSpring, useTransform } from 'framer-motion';
import { cn } from '../../lib/utils';

interface Enhanced3DCardProps {
  children: React.ReactNode;
  className?: string;
  onClick?: () => void;
  variant?: 'glass' | 'holographic' | 'neon' | 'liquid' | 'premium' | 'cyber';
  size?: 'sm' | 'md' | 'lg' | 'xl';
  glow?: boolean;
  floating?: boolean;
  tiltIntensity?: number;
  perspective?: number;
  interactive?: boolean;
}

export const Enhanced3DCard: React.FC<Enhanced3DCardProps> = ({
  children,
  className = '',
  onClick,
  variant = 'glass',
  size = 'md',
  glow = true,
  floating = true,
  tiltIntensity = 1,
  perspective = 1000,
  interactive = true,
}) => {
  const [isHovered, setIsHovered] = useState(false);
  const cardRef = useRef<HTMLDivElement>(null);

  const mouseX = useMotionValue(0);
  const mouseY = useMotionValue(0);
  
  const rotateX = useSpring(
    useTransform(mouseY, [-0.5, 0.5], [15 * tiltIntensity, -15 * tiltIntensity])
  );
  const rotateY = useSpring(
    useTransform(mouseX, [-0.5, 0.5], [-15 * tiltIntensity, 15 * tiltIntensity])
  );

  const variants = {
    glass: {
      base: 'bg-white/10 border-white/20 backdrop-blur-xl',
      glow: 'shadow-white/20',
      hover: 'bg-white/15 border-white/30',
    },
    holographic: {
      base: 'bg-gradient-to-br from-pink-500/20 via-purple-500/20 to-cyan-500/20 border-pink-500/30 backdrop-blur-xl',
      glow: 'shadow-pink-500/30',
      hover: 'from-pink-500/30 via-purple-500/30 to-cyan-500/30 border-pink-500/50',
    },
    neon: {
      base: 'bg-gradient-to-br from-green-500/20 to-emerald-500/20 border-green-500/30 backdrop-blur-xl',
      glow: 'shadow-green-500/30',
      hover: 'from-green-500/30 to-emerald-500/30 border-green-500/50',
    },
    liquid: {
      base: 'bg-gradient-to-br from-blue-500/20 to-purple-500/20 border-blue-500/30 backdrop-blur-xl',
      glow: 'shadow-blue-500/30',
      hover: 'from-blue-500/30 to-purple-500/30 border-blue-500/50',
    },
    premium: {
      base: 'bg-gradient-to-br from-orange-500/20 to-red-500/20 border-orange-500/30 backdrop-blur-xl',
      glow: 'shadow-orange-500/30',
      hover: 'from-orange-500/30 to-red-500/30 border-orange-500/50',
    },
    cyber: {
      base: 'bg-gradient-to-br from-cyan-500/20 to-blue-500/20 border-cyan-500/30 backdrop-blur-xl',
      glow: 'shadow-cyan-500/30',
      hover: 'from-cyan-500/30 to-blue-500/30 border-cyan-500/50',
    },
  };

  const sizes = {
    sm: 'p-4 rounded-xl',
    md: 'p-6 rounded-2xl',
    lg: 'p-8 rounded-3xl',
    xl: 'p-10 rounded-3xl',
  };

  const handleMouseMove = useCallback((e: React.MouseEvent<HTMLDivElement>) => {
    if (!interactive || !cardRef.current) return;

    const rect = cardRef.current.getBoundingClientRect();
    const centerX = rect.left + rect.width / 2;
    const centerY = rect.top + rect.height / 2;
    
    const x = (e.clientX - centerX) / (rect.width / 2);
    const y = (e.clientY - centerY) / (rect.height / 2);
    
    mouseX.set(x);
    mouseY.set(y);
  }, [interactive, mouseX, mouseY]);

  const handleMouseLeave = useCallback(() => {
    mouseX.set(0);
    mouseY.set(0);
    setIsHovered(false);
  }, [mouseX, mouseY]);

  const currentVariant = variants[variant];

  return (
    <motion.div
      ref={cardRef}
      className={cn(
        'relative border cursor-pointer transition-all duration-500 ease-out',
        'transform-gpu will-change-transform',
        currentVariant.base,
        sizes[size],
        glow && `shadow-2xl ${currentVariant.glow}`,
        floating && 'hover:shadow-2xl',
        className
      )}
      style={{
        transformStyle: 'preserve-3d',
        perspective,
        rotateX: interactive ? rotateX : 0,
        rotateY: interactive ? rotateY : 0,
      }}
      initial={{ opacity: 0, y: 20, rotateX: 10 }}
      animate={{ 
        opacity: 1, 
        y: floating && isHovered ? -10 : 0, 
        rotateX: 0,
      }}
      whileHover={onClick ? { scale: 1.02 } : { scale: 1.01 }}
      whileTap={onClick ? { scale: 0.98 } : {}}
      transition={{ 
        type: "spring", 
        stiffness: 300, 
        damping: 30,
        duration: 0.3
      }}
      onMouseMove={handleMouseMove}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={handleMouseLeave}
      onClick={onClick}
    >
      {/* Holographic shimmer overlay */}
      {variant === 'holographic' && (
        <motion.div
          className="absolute inset-0 opacity-0 bg-gradient-to-r from-transparent via-white/20 to-transparent rounded-inherit"
          animate={{
            opacity: isHovered ? [0, 0.5, 0] : 0,
            x: isHovered ? [-100, 300] : -100
          }}
          transition={{ duration: 2, ease: "easeInOut", repeat: isHovered ? Infinity : 0 }}
        />
      )}
      
      {/* Neon glow animation */}
      {variant === 'neon' && isHovered && (
        <motion.div
          className="absolute inset-0 bg-green-400/10 rounded-inherit blur-sm"
          animate={{
            scale: [1, 1.05, 1],
            opacity: [0.3, 0.6, 0.3],
          }}
          transition={{
            duration: 1.5,
            ease: 'easeInOut',
            repeat: Infinity,
          }}
        />
      )}

      {/* Liquid animation */}
      {variant === 'liquid' && (
        <motion.div
          className="absolute inset-0 bg-gradient-to-br from-blue-400/20 to-purple-500/20 rounded-inherit"
          animate={{
            scale: isHovered ? [1, 1.02, 1] : 1,
            rotate: isHovered ? [0, 1, -1, 0] : 0,
          }}
          transition={{
            duration: 3,
            ease: 'easeInOut',
            repeat: isHovered ? Infinity : 0,
          }}
        />
      )}

      {/* Cyber grid effect */}
      {variant === 'cyber' && isHovered && (
        <div className="absolute inset-0 opacity-20 rounded-inherit overflow-hidden">
          <div 
            className="absolute inset-0"
            style={{
              backgroundImage: `
                linear-gradient(rgba(6,182,212,0.3) 1px, transparent 1px),
                linear-gradient(90deg, rgba(6,182,212,0.3) 1px, transparent 1px)
              `,
              backgroundSize: '20px 20px',
            }}
          />
        </div>
      )}

      {/* Premium rotating border */}
      {variant === 'premium' && isHovered && (
        <motion.div
          className="absolute inset-0 rounded-inherit"
          style={{
            background: 'linear-gradient(45deg, transparent, rgba(255,165,0,0.4), transparent)',
            filter: 'blur(1px)',
          }}
          animate={{
            rotate: 360
          }}
          transition={{ duration: 3, ease: "linear", repeat: Infinity }}
        />
      )}

      {/* Floating particles for holographic variant */}
      {variant === 'holographic' && isHovered && (
        <div className="absolute inset-0 pointer-events-none rounded-inherit overflow-hidden">
          {[...Array(8)].map((_, i) => (
            <motion.div
              key={i}
              className="absolute w-1 h-1 bg-cyan-400 rounded-full"
              initial={{
                x: Math.random() * 100 + '%',
                y: '100%',
                opacity: 0
              }}
              animate={{
                y: '-10%',
                opacity: [0, 1, 0]
              }}
              transition={{
                duration: 2,
                delay: i * 0.2,
                repeat: Infinity
              }}
            />
          ))}
        </div>
      )}

      {/* Content with 3D transform */}
      <motion.div
        className="relative z-10"
        style={{
          transform: isHovered ? 'translateZ(20px)' : 'translateZ(0px)',
          transformStyle: 'preserve-3d'
        }}
        transition={{ duration: 0.3 }}
      >
        {children}
      </motion.div>

      {/* 3D depth shadow */}
      <motion.div
        className="absolute inset-0 bg-black/20 rounded-inherit"
        style={{
          transform: 'translateZ(-10px)',
        }}
        animate={{
          opacity: isHovered ? 0.3 : 0.1,
        }}
        transition={{ duration: 0.3 }}
      />

      {/* Interactive light reflection */}
      {interactive && (
        <motion.div
          className="absolute inset-0 rounded-inherit pointer-events-none"
          style={{
            background: `radial-gradient(circle at ${mouseX.get() * 50 + 50}% ${mouseY.get() * 50 + 50}%, rgba(255,255,255,0.1) 0%, transparent 50%)`,
            opacity: isHovered ? 1 : 0,
          }}
          transition={{ duration: 0.2 }}
        />
      )}
    </motion.div>
  );
};

export default Enhanced3DCard;
