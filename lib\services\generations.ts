import { supabase } from '../supabase'
import type { Database } from '../database.types'

type Generation = Database['public']['Tables']['generations']['Row']
type GenerationInsert = Database['public']['Tables']['generations']['Insert']
type GenerationUpdate = Database['public']['Tables']['generations']['Update']

export const generationsService = {
  async createGeneration(data: GenerationInsert) {
    const { data: generation, error } = await supabase
      .from('generations')
      .insert(data)
      .select()
      .single()
    
    if (error) throw error
    return generation
  },

  async getUserGenerations(userId: string, type?: string, limit = 50) {
    let query = supabase
      .from('generations')
      .select('*')
      .eq('user_id', userId)
      .order('created_at', { ascending: false })
      .limit(limit)

    if (type) {
      query = query.eq('type', type)
    }

    const { data, error } = await query
    if (error) throw error
    return data
  },

  async getGeneration(id: string) {
    const { data, error } = await supabase
      .from('generations')
      .select('*')
      .eq('id', id)
      .single()
    
    if (error) throw error
    return data
  },

  async updateGeneration(id: string, updates: GenerationUpdate) {
    const { data, error } = await supabase
      .from('generations')
      .update(updates)
      .eq('id', id)
      .select()
      .single()
    
    if (error) throw error
    return data
  },

  async deleteGeneration(id: string) {
    const { error } = await supabase
      .from('generations')
      .delete()
      .eq('id', id)
    
    if (error) throw error
  },

  async toggleFavorite(id: string, isFavorite: boolean) {
    return this.updateGeneration(id, { is_favorite: !isFavorite })
  },

  async incrementUsage(id: string) {
    const { data: current } = await supabase
      .from('generations')
      .select('usage_count')
      .eq('id', id)
      .single()

    if (current) {
      return this.updateGeneration(id, { 
        usage_count: current.usage_count + 1 
      })
    }
  },

  async getFavorites(userId: string) {
    const { data, error } = await supabase
      .from('generations')
      .select('*')
      .eq('user_id', userId)
      .eq('is_favorite', true)
      .order('created_at', { ascending: false })
    
    if (error) throw error
    return data
  },

  async getStats(userId: string) {
    const { data, error } = await supabase
      .from('generations')
      .select('type, count(*)')
      .eq('user_id', userId)
      .group('type')
    
    if (error) throw error
    return data
  }
}
