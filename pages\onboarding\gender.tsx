import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { PapoFireLogo } from '../../components/ui/icons';

const OnboardingGenderPage = () => {
  const [selected, setSelected] = useState<string | null>(null);

  return (
    <div className="flex flex-col items-center justify-center min-h-screen bg-black text-white p-4">
      <div className="text-center mb-10">
        <PapoFireLogo />
        <h1 className="text-4xl font-bold mt-4">Quase lá!</h1>
        <p className="text-lg text-gray-400 mt-2">Para te dar as melhores respostas, precisamos saber:</p>
      </div>
      <div className="flex space-x-6">
        <motion.div
          onClick={() => setSelected('male')}
          className={`w-48 h-48 rounded-2xl flex flex-col items-center justify-center cursor-pointer border-2 ${selected === 'male' ? 'border-blue-500 bg-blue-500/10' : 'border-gray-800 bg-gray-900/50'}`}
          whileHover={{ scale: 1.05 }}
          transition={{ type: 'spring', stiffness: 300 }}
        >
          <span className="text-6xl">👨</span>
          <span className="mt-4 text-2xl font-bold">Homem</span>
        </motion.div>
        <motion.div
          onClick={() => setSelected('female')}
          className={`w-48 h-48 rounded-2xl flex flex-col items-center justify-center cursor-pointer border-2 ${selected === 'female' ? 'border-pink-500 bg-pink-500/10' : 'border-gray-800 bg-gray-900/50'}`}
          whileHover={{ scale: 1.05 }}
          transition={{ type: 'spring', stiffness: 300 }}
        >
          <span className="text-6xl">👩</span>
          <span className="mt-4 text-2xl font-bold">Mulher</span>
        </motion.div>
      </div>
    </div>
  );
};

export default OnboardingGenderPage;