import React from 'react';
import type { ToneId } from '../../types';
import { TONES } from '../../constants';
import { LiquidGlassButton } from '../ShadcnUI';

interface ToneSelectorProps {
  onSelect: (tone: ToneId) => void;
  disabled?: boolean;
}

export const ToneSelector: React.FC<ToneSelectorProps> = ({ onSelect, disabled }) => {
  return (
    <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-5 gap-2">
      {TONES.map(tone => (
        <LiquidGlassButton 
          key={tone.id} 
          onClick={() => onSelect(tone.id)} 
          disabled={disabled} 
          className="py-2 text-sm"
        >
          {tone.label}
        </LiquidGlassButton>
      ))}
    </div>
  );
};
