
export enum Page {
  Landing = 'landing',
  Home = 'home',
  Dashboard = 'dashboard',
  Generator = 'generator',
  GeneratorCompliment = 'generator_compliment',
  GeneratorStory = 'generator_story',
  GeneratorChat = 'generator_chat',
  GeneratorDateIdea = 'generator_date_idea',
  Roleta = 'roleta',
  Upgrade = 'upgrade',
  GenderSelection = 'gender_selection',
}

export enum GeneratorMode {
  Topic = 'topic',
  Story = 'story',
  Chat = 'chat',
  Compliment = 'compliment',
  DateIdea = 'date_idea',
}

export enum SocialPlatformId {
  WhatsApp = 'whatsapp',
  Instagram = 'instagram',
  TikTok = 'tiktok',
  Twitter = 'twitter',
}

export enum ToneId {
  Casual = 'casual',
  Flerte = 'flerte',
  Fofo = 'fofo',
  Confiante = 'confiante',
  RomanticoSutil = 'romantico_sutil',
}

export enum Gender {
  Male = 'male',
  Female = 'female',
}

export interface User {
  email: string;
  gender?: Gender;
}

export interface UploadedFile {
  file: File;
  preview: string;
}

export interface GenerationResult {
  id: string;
  text: string;
}
