import React, { useEffect, useState, useMemo } from 'react';
import { motion } from 'framer-motion';

interface AnimatedBackgroundProps {
  variant?: 'gradient' | 'particles' | 'waves' | 'geometric' | 'neural' | 'matrix';
  intensity?: 'low' | 'medium' | 'high';
  colors?: string[];
  className?: string;
  interactive?: boolean;
}

export const AnimatedBackground: React.FC<AnimatedBackgroundProps> = ({
  variant = 'gradient',
  intensity = 'medium',
  colors = ['#ff3d00', '#ff5722', '#9c27b0', '#673ab7', '#3f51b5', '#2196f3'],
  className = '',
  interactive = false,
}) => {
  const [mousePos, setMousePos] = useState({ x: 0, y: 0 });

  const intensityConfig = {
    low: { count: 20, speed: 0.5, size: 0.8 },
    medium: { count: 40, speed: 1, size: 1 },
    high: { count: 60, speed: 1.5, size: 1.2 },
  };

  const config = intensityConfig[intensity];

  const handleMouseMove = (e: React.MouseEvent) => {
    if (interactive) {
      setMousePos({
        x: e.clientX / window.innerWidth,
        y: e.clientY / window.innerHeight,
      });
    }
  };

  const gradientElements = useMemo(() => {
    return Array.from({ length: 5 }, (_, i) => ({
      id: i,
      color: colors[i % colors.length],
      size: Math.random() * 400 + 200,
      x: Math.random() * 100,
      y: Math.random() * 100,
      duration: Math.random() * 20 + 10,
    }));
  }, [colors]);

  const particleElements = useMemo(() => {
    return Array.from({ length: config.count }, (_, i) => ({
      id: i,
      x: Math.random() * 100,
      y: Math.random() * 100,
      size: Math.random() * 4 + 2,
      color: colors[Math.floor(Math.random() * colors.length)],
      duration: Math.random() * 10 + 5,
      delay: Math.random() * 5,
    }));
  }, [config.count, colors]);

  const waveElements = useMemo(() => {
    return Array.from({ length: 4 }, (_, i) => ({
      id: i,
      amplitude: Math.random() * 50 + 30,
      frequency: Math.random() * 0.02 + 0.01,
      phase: Math.random() * Math.PI * 2,
      color: colors[i % colors.length],
      opacity: 0.1 + Math.random() * 0.2,
    }));
  }, [colors]);

  const geometricElements = useMemo(() => {
    return Array.from({ length: 15 }, (_, i) => ({
      id: i,
      x: Math.random() * 100,
      y: Math.random() * 100,
      size: Math.random() * 100 + 50,
      rotation: Math.random() * 360,
      color: colors[Math.floor(Math.random() * colors.length)],
      shape: ['circle', 'square', 'triangle'][Math.floor(Math.random() * 3)],
      duration: Math.random() * 15 + 10,
    }));
  }, [colors]);

  const renderGradientBackground = () => (
    <div className="absolute inset-0 overflow-hidden">
      {gradientElements.map((element) => (
        <motion.div
          key={element.id}
          className="absolute rounded-full blur-3xl opacity-20"
          style={{
            background: `radial-gradient(circle, ${element.color}40 0%, transparent 70%)`,
            width: element.size * config.size,
            height: element.size * config.size,
            left: `${element.x}%`,
            top: `${element.y}%`,
          }}
          animate={{
            x: ['-20%', '20%', '-20%'],
            y: ['-20%', '20%', '-20%'],
            scale: [1, 1.2, 1],
          }}
          transition={{
            duration: element.duration * config.speed,
            repeat: Infinity,
            ease: 'easeInOut',
          }}
        />
      ))}
      {interactive && (
        <motion.div
          className="absolute w-96 h-96 rounded-full blur-3xl opacity-30"
          style={{
            background: `radial-gradient(circle, ${colors[0]}60 0%, transparent 70%)`,
            left: `${mousePos.x * 100}%`,
            top: `${mousePos.y * 100}%`,
            transform: 'translate(-50%, -50%)',
          }}
          animate={{
            scale: [1, 1.1, 1],
          }}
          transition={{
            duration: 2,
            repeat: Infinity,
            ease: 'easeInOut',
          }}
        />
      )}
    </div>
  );

  const renderParticlesBackground = () => (
    <div className="absolute inset-0 overflow-hidden">
      {particleElements.map((particle) => (
        <motion.div
          key={particle.id}
          className="absolute rounded-full"
          style={{
            width: particle.size * config.size,
            height: particle.size * config.size,
            background: `radial-gradient(circle, ${particle.color}80 0%, transparent 70%)`,
            left: `${particle.x}%`,
            top: `${particle.y}%`,
          }}
          animate={{
            y: [0, -100, 0],
            x: [0, Math.sin(particle.id) * 50, 0],
            opacity: [0.2, 0.8, 0.2],
            scale: [0.5, 1, 0.5],
          }}
          transition={{
            duration: particle.duration * config.speed,
            repeat: Infinity,
            delay: particle.delay,
            ease: 'easeInOut',
          }}
        />
      ))}
    </div>
  );

  const renderWavesBackground = () => (
    <div className="absolute inset-0 overflow-hidden">
      <svg className="absolute inset-0 w-full h-full">
        {waveElements.map((wave, index) => (
          <motion.path
            key={wave.id}
            d={`M0,${50 + index * 20} Q${25},${50 + index * 20 - wave.amplitude} ${50},${50 + index * 20} T${100},${50 + index * 20}`}
            fill="none"
            stroke={wave.color}
            strokeWidth="2"
            opacity={wave.opacity}
            vectorEffect="non-scaling-stroke"
            animate={{
              d: [
                `M0,${50 + index * 20} Q${25},${50 + index * 20 - wave.amplitude} ${50},${50 + index * 20} T${100},${50 + index * 20}`,
                `M0,${50 + index * 20} Q${25},${50 + index * 20 + wave.amplitude} ${50},${50 + index * 20} T${100},${50 + index * 20}`,
                `M0,${50 + index * 20} Q${25},${50 + index * 20 - wave.amplitude} ${50},${50 + index * 20} T${100},${50 + index * 20}`,
              ],
            }}
            transition={{
              duration: 4 + index,
              repeat: Infinity,
              ease: 'easeInOut',
            }}
          />
        ))}
      </svg>
    </div>
  );

  const renderGeometricBackground = () => (
    <div className="absolute inset-0 overflow-hidden">
      {geometricElements.map((element) => (
        <motion.div
          key={element.id}
          className="absolute opacity-10"
          style={{
            left: `${element.x}%`,
            top: `${element.y}%`,
            width: element.size * config.size,
            height: element.size * config.size,
          }}
          animate={{
            rotate: [0, 360],
            scale: [1, 1.2, 1],
            opacity: [0.05, 0.15, 0.05],
          }}
          transition={{
            duration: element.duration * config.speed,
            repeat: Infinity,
            ease: 'linear',
          }}
        >
          {element.shape === 'circle' && (
            <div
              className="w-full h-full rounded-full border-2"
              style={{ borderColor: element.color }}
            />
          )}
          {element.shape === 'square' && (
            <div
              className="w-full h-full border-2"
              style={{ borderColor: element.color }}
            />
          )}
          {element.shape === 'triangle' && (
            <div
              className="w-0 h-0 border-l-[50px] border-r-[50px] border-b-[87px] border-l-transparent border-r-transparent"
              style={{ borderBottomColor: element.color }}
            />
          )}
        </motion.div>
      ))}
    </div>
  );

  const renderNeuralBackground = () => (
    <div className="absolute inset-0 overflow-hidden">
      <svg className="absolute inset-0 w-full h-full">
        {Array.from({ length: config.count }, (_, i) => {
          const x1 = Math.random() * 100;
          const y1 = Math.random() * 100;
          const x2 = Math.random() * 100;
          const y2 = Math.random() * 100;
          return (
            <motion.line
              key={i}
              x1={`${x1}%`}
              y1={`${y1}%`}
              x2={`${x2}%`}
              y2={`${y2}%`}
              stroke={colors[i % colors.length]}
              strokeWidth="1"
              opacity="0.1"
              animate={{
                opacity: [0.05, 0.2, 0.05],
              }}
              transition={{
                duration: Math.random() * 3 + 2,
                repeat: Infinity,
                delay: Math.random() * 2,
              }}
            />
          );
        })}
        {Array.from({ length: config.count / 2 }, (_, i) => {
          const cx = Math.random() * 100;
          const cy = Math.random() * 100;
          return (
            <motion.circle
              key={`node-${i}`}
              cx={`${cx}%`}
              cy={`${cy}%`}
              r="2"
              fill={colors[i % colors.length]}
              opacity="0.3"
              animate={{
                r: [1, 3, 1],
                opacity: [0.2, 0.6, 0.2],
              }}
              transition={{
                duration: Math.random() * 2 + 1,
                repeat: Infinity,
                delay: Math.random(),
              }}
            />
          );
        })}
      </svg>
    </div>
  );

  const renderMatrixBackground = () => (
    <div className="absolute inset-0 overflow-hidden opacity-20">
      {Array.from({ length: 20 }, (_, i) => (
        <motion.div
          key={i}
          className="absolute text-green-400 font-mono text-sm"
          style={{
            left: `${(i * 5) % 100}%`,
            top: '-10%',
          }}
          animate={{
            y: ['0vh', '110vh'],
          }}
          transition={{
            duration: Math.random() * 3 + 2,
            repeat: Infinity,
            delay: Math.random() * 2,
            ease: 'linear',
          }}
        >
          {Array.from({ length: 20 }, (_, j) => (
            <div key={j} className="mb-2">
              {Math.random() > 0.5 ? '1' : '0'}
            </div>
          ))}
        </motion.div>
      ))}
    </div>
  );

  const renderBackground = () => {
    switch (variant) {
      case 'gradient':
        return renderGradientBackground();
      case 'particles':
        return renderParticlesBackground();
      case 'waves':
        return renderWavesBackground();
      case 'geometric':
        return renderGeometricBackground();
      case 'neural':
        return renderNeuralBackground();
      case 'matrix':
        return renderMatrixBackground();
      default:
        return renderGradientBackground();
    }
  };

  return (
    <div
      className={`fixed inset-0 pointer-events-none z-0 ${className}`}
      onMouseMove={handleMouseMove}
    >
      {renderBackground()}
    </div>
  );
};

export default AnimatedBackground;
