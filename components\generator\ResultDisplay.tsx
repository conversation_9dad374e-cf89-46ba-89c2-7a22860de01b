import React from 'react';

interface ResultDisplayProps {
  results: string[];
  isLoading: boolean;
}

export const ResultDisplay: React.FC<ResultDisplayProps> = ({ results, isLoading }) => {
  if (isLoading) {
    return <div>Loading...</div>;
  }

  if (results.length === 0) {
    return null;
  }

  return (
    <div className="mt-4">
      <h3 className="text-lg font-semibold">Generated Ideas:</h3>
      <ul className="list-disc list-inside">
        {results.map((idea, index) => (
          <li key={index}>{idea}</li>
        ))}
      </ul>
    </div>
  );
};