import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { ChevronRight, Sparkles, Zap, Heart, MessageCircle, Star, Users, Check, BrainCircuit, BotMessageSquare, Calendar, Instagram, Twitter, MessageSquare, ThumbsUp, Flame } from 'lucide-react';
import { GlowingButton } from '../../components/ui/glowing-effect';
import { Page } from '../../types';
import { SquaresBackground } from '../../components/ui/squares-background';
import { AnimatedShinyText } from '../../components/ui/animated-shiny-text';
import { PapoFireLogo, BrainCircuitIcon, ConversationIcon, CrownIcon, IdeaIcon, WhatsAppIcon, TikTokIcon, TwitterIcon, InstagramIcon } from '../../components/ui/icons';
import { GlassCard } from '../../components/glass-card';
import { FluidBlob } from '../../components/fluid-blob';
import { LiquidButton } from '../../components/liquid-glass-button';
import { GradientButton } from '../../components/gradient-button';
import { GlowingEffect } from '../../components/glowing-effect';
import { VapourTextEffect } from '../../components/vapour-text-effect';

interface LandingPageProps {
  onLoginClick: () => void;
  onNavigate: (page: Page) => void;
}

export const LandingPage: React.FC<LandingPageProps> = ({ onLoginClick, onNavigate }) => {
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });
  const [isLoaded, setIsLoaded] = useState(false);

  useEffect(() => {
    setIsLoaded(true);
    
    const handleMouseMove = (e: MouseEvent) => {
      setMousePosition({ x: e.clientX, y: e.clientY });
    };

    window.addEventListener('mousemove', handleMouseMove);
    return () => window.removeEventListener('mousemove', handleMouseMove);
  }, []);

  const features = [
    { 
      icon: <BrainCircuit className="w-6 h-6" />, 
      title: 'IA por Gênero', 
      desc: 'Respostas customizadas para homem e mulher.',
      color: 'fire' as const
    },
    { 
      icon: <BotMessageSquare className="w-6 h-6" />, 
      title: 'Gírias Brasileiras', 
      desc: 'Linguagem natural que realmente funciona.',
      color: 'cosmic' as const
    },
    { 
      icon: <ThumbsUp className="w-6 h-6" />, 
      title: 'Whatsapp Ready', 
      desc: 'Copie e cole direto no seu chat.',
      color: 'electric' as const
    },
  ];

  const plans = [
    {
      name: 'PapoFire PRO',
      price: 'R$19',
      features: ['Gerações Ilimitadas', 'Todos os Modos de IA', 'Análise de Imagem', 'Suporte Prioritário'],
      popular: true,
      color: 'fire' as const
    },
    {
      name: 'PapoFire ULTRA',
      price: 'R$49',
      features: ['Tudo do PRO', 'Análise de Perfil', 'Tom de Voz Personalizado', 'Consultoria com IA'],
      popular: false,
      color: 'cosmic' as const
    },
  ];

  return (
    <div className="min-h-screen bg-black text-white relative overflow-hidden">
      {/* Animated background with 4D effects */}
      <div className="absolute inset-0 overflow-hidden">
        <FluidBlob 
          size="xl" 
          color="gradient" 
          animated={true}
          className="absolute top-10 left-10 opacity-20" 
        />
        <FluidBlob 
          size="lg" 
          color="orange" 
          animated={true}
          className="absolute top-1/3 right-20 opacity-15" 
        />
        <FluidBlob 
          size="md" 
          color="purple" 
          animated={true}
          className="absolute bottom-20 left-1/4 opacity-25" 
        />
        
        {/* Floating particles */}
        <div className="absolute inset-0 pointer-events-none">
          {[...Array(30)].map((_, i) => (
            <motion.div
              key={i}
              className="absolute w-1 h-1 bg-orange-400/30 rounded-full"
              style={{
                left: `${Math.random() * 100}%`,
                top: `${Math.random() * 100}%`
              }}
              animate={{
                y: [-20, 20, -20],
                x: [-10, 10, -10],
                opacity: [0.3, 0.8, 0.3],
                scale: [0.5, 1.5, 0.5]
              }}
              transition={{
                duration: 4 + i * 0.2,
                repeat: Infinity,
                ease: "easeInOut"
              }}
            />
          ))}
        </div>
      </div>

      {/* Mouse follower effect */}
      <motion.div
        className="fixed w-96 h-96 rounded-full bg-orange-500/10 blur-3xl pointer-events-none z-0"
        style={{
          left: mousePosition.x - 192,
          top: mousePosition.y - 192
        }}
        animate={{
          scale: [1, 1.2, 1],
          opacity: [0.1, 0.3, 0.1]
        }}
        transition={{
          duration: 3,
          repeat: Infinity,
          ease: "easeInOut"
        }}
      />

      <div className="relative z-10 max-w-6xl mx-auto px-4 py-10">
        {/* Header with 4D effects */}
        <motion.header 
          className="flex justify-between items-center"
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: isLoaded ? 1 : 0, y: isLoaded ? 0 : -20 }}
          transition={{ duration: 0.8 }}
        >
          <GlowingEffect color="orange" size="sm" className="flex items-center">
            <PapoFireLogo />
          </GlowingEffect>
          
          <nav className="hidden md:flex items-center gap-6">
            <motion.a 
              href="#" 
              className="hover:text-orange-400 transition-colors"
              whileHover={{ scale: 1.1, rotateY: 5 }}
            >
              Funcionalidades
            </motion.a>
            <motion.a 
              href="#" 
              className="hover:text-orange-400 transition-colors"
              whileHover={{ scale: 1.1, rotateY: 5 }}
            >
              Preços
            </motion.a>
            <motion.a 
              href="#" 
              className="hover:text-orange-400 transition-colors"
              whileHover={{ scale: 1.1, rotateY: 5 }}
            >
              Contato
            </motion.a>
          </nav>
          
          <LiquidButton 
            variant="fire" 
            size="lg"
            onClick={onLoginClick}
          >
            <Sparkles className="w-5 h-5" />
            Login
          </LiquidButton>
        </motion.header>

        {/* Hero Section with extreme 4D effects */}
        <motion.div 
          className="text-center mt-20"
          initial={{ opacity: 0, y: 50, rotateX: -30 }}
          animate={{ opacity: 1, y: 0, rotateX: 0 }}
          transition={{ duration: 0.8 }}
          style={{ transformStyle: 'preserve-3d' }}
        >
          <motion.div 
            className="inline-block bg-orange-500/10 border border-orange-500/30 rounded-full px-4 py-1 text-sm text-orange-400 mb-4"
            whileHover={{ scale: 1.1, rotateZ: 5 }}
          >
            Powered by IA
          </motion.div>
          
          <GlowingEffect 
            color="orange" 
            size="xl" 
            className="mb-4 inline-block"
            pulsing={true}
            floating={true}
          >
            <VapourTextEffect 
              text="PapoFire" 
              color="fire" 
              size="2xl" 
              intensity="high"
              className="text-5xl md:text-7xl font-bold"
            />
          </GlowingEffect>
          
          <motion.p 
            className="text-xl md:text-2xl text-gray-300 mb-6 max-w-3xl mx-auto"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.3 }}
          >
            Gerador multi-plataforma com{' '}
            <VapourTextEffect 
              text="IA brasileira ultra-humanizada" 
              color="fire" 
              size="lg" 
              className="inline"
            />
          </motion.p>
          
          <motion.div 
            className="flex justify-center items-center gap-4 mb-8 text-gray-400"
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.8, delay: 0.5 }}
          >
            <GlowingEffect color="green" size="xs">
              <WhatsAppIcon className="w-6 h-6" />
            </GlowingEffect>
            <GlowingEffect color="pink" size="xs">
              <InstagramIcon className="w-6 h-6" />
            </GlowingEffect>
            <GlowingEffect color="purple" size="xs">
              <TikTokIcon className="w-6 h-6" />
            </GlowingEffect>
            <GlowingEffect color="blue" size="xs">
              <TwitterIcon className="w-6 h-6" />
            </GlowingEffect>
            <span>Gere conversas perfeitas para cada app.</span>
          </motion.div>
          
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.7 }}
          >
            <GradientButton
              variant="fire"
              size="xl"
              onClick={onLoginClick}
              className="px-10 py-4 text-lg"
            >
              <Flame className="w-6 h-6" />
              Usar PapoFire
            </GradientButton>
          </motion.div>
        </motion.div>

        {/* Features with 4D cards */}
        <motion.div 
          className="grid md:grid-cols-3 gap-8 mt-20"
          initial={{ opacity: 0, y: 50 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4, duration: 0.8 }}
        >
          {features.map((feature, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 50, rotateX: -30 }}
              animate={{ opacity: 1, y: 0, rotateX: 0 }}
              transition={{ 
                duration: 0.8, 
                delay: 0.6 + index * 0.1,
                ease: "easeOut"
              }}
              whileHover={{ 
                scale: 1.05,
                rotateY: 10,
                rotateX: 5
              }}
              style={{ transformStyle: 'preserve-3d' }}
            >
              <GlassCard
                variant="holographic"
                size="lg"
                glow={true}
                floating={true}
                className="text-center h-full"
              >
                <GlowingEffect 
                  color={feature.color === 'fire' ? 'orange' : 
                         feature.color === 'cosmic' ? 'purple' : 'cyan'}
                  size="sm"
                  className="w-12 h-12 bg-gray-800 rounded-full flex items-center justify-center mx-auto mb-4"
                >
                  {feature.icon}
                </GlowingEffect>
                
                <VapourTextEffect 
                  text={feature.title}
                  color={feature.color}
                  size="lg"
                  className="mb-2"
                />
                
                <p className="text-gray-400 text-sm">{feature.desc}</p>
              </GlassCard>
            </motion.div>
          ))}
        </motion.div>
        
        {/* Stats with 4D effects */}
        <motion.div 
          className="flex justify-center gap-8 mt-12 text-center"
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ delay: 0.8, duration: 0.8 }}
        >
          <GlowingEffect color="orange" size="sm" floating={true}>
            <div>
              <VapourTextEffect 
                text="10K+" 
                color="fire" 
                size="xl" 
                className="text-3xl font-bold"
              />
              <p className="text-gray-400">Conversas/mês</p>
            </div>
          </GlowingEffect>
          
          <GlowingEffect color="green" size="sm" floating={true}>
            <div>
              <VapourTextEffect 
                text="95%" 
                color="nature" 
                size="xl" 
                className="text-3xl font-bold"
              />
              <p className="text-gray-400">Taxa de Sucesso</p>
            </div>
          </GlowingEffect>
          
          <GlowingEffect color="blue" size="sm" floating={true}>
            <div>
              <VapourTextEffect 
                text="24/7" 
                color="ice" 
                size="xl" 
                className="text-3xl font-bold"
              />
              <p className="text-gray-400">IA Disponível</p>
            </div>
          </GlowingEffect>
        </motion.div>

        {/* Pricing with 4D cards */}
        <motion.div 
          className="grid md:grid-cols-2 gap-8 mt-20"
          initial={{ opacity: 0, y: 50 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.6, duration: 0.8 }}
        >
          {plans.map((plan, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 50, rotateX: -30 }}
              animate={{ opacity: 1, y: 0, rotateX: 0 }}
              transition={{ 
                duration: 0.8, 
                delay: 1.0 + index * 0.2,
                ease: "easeOut"
              }}
              whileHover={{ 
                scale: 1.05,
                rotateY: 5,
                rotateX: 5
              }}
              style={{ transformStyle: 'preserve-3d' }}
            >
              <GlassCard
                variant={plan.popular ? "holographic" : "glass"}
                size="xl"
                glow={plan.popular}
                floating={true}
                className="relative"
              >
                {plan.popular && (
                  <div className="text-center mb-4">
                    <span className="bg-orange-500 text-white text-xs font-bold px-3 py-1 rounded-full">
                      Mais Popular
                    </span>
                  </div>
                )}
                
                <VapourTextEffect 
                  text={plan.name}
                  color={plan.color}
                  size="xl"
                  className="text-3xl font-bold text-center mb-2"
                />
                
                <p className="text-5xl font-bold text-center mb-6">
                  {plan.price}
                  <span className="text-lg text-gray-400">/mês</span>
                </p>
                
                <ul className="space-y-4 mb-8">
                  {plan.features.map((feature, i) => (
                    <motion.li 
                      key={i} 
                      className="flex items-center gap-3"
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ delay: 1.2 + i * 0.1 }}
                    >
                      <Check className="w-5 h-5 text-green-500" />
                      <span>{feature}</span>
                    </motion.li>
                  ))}
                </ul>
                
                <GradientButton
                  variant={plan.popular ? "fire" : "cosmic"}
                  size="lg"
                  className="w-full py-3 text-lg"
                >
                  Escolher Plano
                </GradientButton>
              </GlassCard>
            </motion.div>
          ))}
        </motion.div>

        {/* Footer with 4D effects */}
        <motion.div 
          className="text-center mt-12"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 1.4, duration: 0.8 }}
        >
          <motion.a 
            href="#" 
            className="text-gray-400 hover:text-white underline"
            whileHover={{ scale: 1.1, rotateY: 5 }}
          >
            Ver todos os Planos
          </motion.a>
        </motion.div>
        
        <motion.div 
          className="text-center mt-8 text-gray-400"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 1.6, duration: 0.8 }}
        >
          <span>Já tenho uma conta? </span>
          <motion.a 
            href="#" 
            onClick={onLoginClick} 
            className="text-orange-400 hover:text-orange-300 font-bold"
            whileHover={{ scale: 1.1, rotateY: 5 }}
          >
            Fazer login
          </motion.a>
        </motion.div>
      </div>
    </div>
  );
};

export default LandingPage;
