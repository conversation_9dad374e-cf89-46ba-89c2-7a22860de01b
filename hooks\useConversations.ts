import { useEffect, useState } from 'react';
import { supabase } from '../lib/supabaseClient';

export function useConversations(user_id: string | null) {
  const [conversations, setConversations] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (!user_id) return;
    setLoading(true);
    supabase
      .from('conversations')
      .select('*')
      .eq('user_id', user_id)
      .order('created_at', { ascending: false })
      .then(({ data, error }) => {
        setConversations(data || []);
        setError(error ? error.message : null);
        setLoading(false);
      });
  }, [user_id]);

  // Função para criar nova conversa
  const createConversation = async (title: string) => {
    const { data, error } = await supabase
      .from('conversations')
      .insert([{ user_id, title }])
      .select();
    if (data) setConversations((prev) => [data[0], ...prev]);
    if (error) setError(error.message);
    return { data, error };
  };

  // Função para deletar conversa
  const deleteConversation = async (id: string) => {
    const { error } = await supabase
      .from('conversations')
      .delete()
      .eq('id', id);
    setConversations((prev) => prev.filter((c) => c.id !== id));
    if (error) setError(error.message);
    return { error };
  };

  return { conversations, loading, error, createConversation, deleteConversation };
} 