import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { ChevronLeft, Lightbulb, Loader2, <PERSON><PERSON>, Check } from 'lucide-react';
import { GlassCard } from '../../components/glass-card';
import { GlowingButton } from '../../components/ui/glowing-effect';

const DateIdeaGeneratorPage: React.FC<{ onBack: () => void }> = ({ onBack }) => {
  const [topic, setTopic] = useState('');
  const [results, setResults] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [copiedText, setCopiedText] = useState<string | null>(null);

  const handleGeneration = async () => {
    if (!topic) return;
    setIsLoading(true);
    setResults([]);
    try {
      await new Promise(resolve => setTimeout(resolve, 2000));
      const generated = [
        `Piquenique no parque com direito a pôr do sol.`,
        `Noite de jogos de tabuleiro em casa com petiscos e bebidas.`,
        `Visita a um museu ou galeria de arte.`,
        `Aula de culinária para casais.`,
        `Passeio de bicicleta pela cidade e um sorvete no final.`
      ];
      setResults(generated);
    } catch (e) { console.error(e); }
    finally { setIsLoading(false); }
  };

  const handleCopy = (text: string) => {
    navigator.clipboard.writeText(text);
    setCopiedText(text);
    setTimeout(() => setCopiedText(null), 2000);
  };

  return (
    <motion.div
      className="min-h-screen p-4 bg-gradient-to-br from-green-900 via-black to-blue-900 text-white relative overflow-hidden"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.5 }}
    >
      <div className="relative z-10 max-w-md mx-auto">
        <div className="flex items-center mb-8 pt-8">
          <motion.button
            onClick={onBack}
            className="mr-4 p-3 rounded-full hover:bg-gray-800/50 transition-all cursor-hover"
            whileHover={{ scale: 1.1, rotate: -10 }}
            whileTap={{ scale: 0.9 }}
          >
            <ChevronLeft className="w-6 h-6" />
          </motion.button>
          <div>
            <h1 className="text-2xl font-bold">Ideias para Encontros 💡</h1>
            <p className="text-sm text-gray-400">Sugestões para momentos inesquecíveis</p>
          </div>
        </div>

        <GlassCard>
          <div className="space-y-4">
            <h3 className="font-bold text-lg">Que tipo de encontro você procura?</h3>
            <textarea
              value={topic}
              onChange={(e) => setTopic(e.target.value)}
              placeholder="Ex: Romântico, casual, divertido, barato..."
              className="w-full p-4 bg-input-background rounded-lg border border-input-border focus:border-brand-orange focus:outline-none resize-none text-text-primary"
              rows={3}
            />
            <GlowingButton
              onClick={handleGeneration}
              disabled={isLoading || !topic}
              className="w-full py-3"
            >
              {isLoading ? (
                <span className="flex items-center gap-2">
                  <Loader2 className="w-5 h-5 animate-spin" />
                  Gerando ideias...
                </span>
              ) : (
                <span className="flex items-center gap-2">
                  <Lightbulb className="w-5 h-5" />
                  Gerar Ideias
                </span>
              )}
            </GlowingButton>
          </div>
        </GlassCard>

        {isLoading && (
          <div className="flex items-center justify-center text-center p-8 space-x-4">
            <Loader2 className="w-8 h-8 animate-spin text-brand-orange" />
            <p className="text-lg font-semibold text-text-secondary">Buscando as melhores ideias...</p>
          </div>
        )}

        {results.length > 0 && !isLoading && (
          <div className="space-y-4 mt-8">
            {results.map((result, index) => (
              <GlassCard key={index} className="flex items-center justify-between">
                <p className="text-gray-300">{result}</p>
                <button onClick={() => handleCopy(result)}>
                  {copiedText === result ? <Check className="w-5 h-5 text-green-500" /> : <Copy className="w-5 h-5 text-gray-400" />}
                </button>
              </GlassCard>
            ))}
          </div>
        )}
      </div>
    </motion.div>
  );
};

export default DateIdeaGeneratorPage;