import React from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { <PERSON>, Mail, Lock, Eye, EyeOff, X, Check } from 'lucide-react';

// Exemplo de props, ajuste conforme necessário
const AuthModal = ({
  show,
  onClose,
  isLogin,
  setIsLogin,
  email,
  setEmail,
  password,
  setPassword,
  confirmPassword,
  setConfirmPassword,
  showPassword,
  setShowPassword,
  gender,
  setGender,
  authError,
  handleAuth,
  inputConfig,
  emailRef,
  passwordRef,
  confirmPasswordRef,
}) => {
  return (
    <AnimatePresence>
      {show && (
        <motion.div
          className="fixed inset-0 bg-black/80 backdrop-blur-md flex items-center justify-center z-50 p-4"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          onClick={(e) => e.target === e.currentTarget && onClose()}
        >
          <motion.div
            className="bg-gradient-to-br from-gray-900/95 to-black/95 backdrop-blur-xl rounded-3xl p-8 w-full max-w-md border border-white/10"
            initial={{ scale: 0.8, y: 50, opacity: 0 }}
            animate={{ scale: 1, y: 0, opacity: 1 }}
            exit={{ scale: 0.8, y: -50, opacity: 0 }}
            transition={{ type: 'spring', stiffness: 300 }}
          >
            {/* Header do Modal */}
            <div className="text-center mb-8">
              <Flame className="w-16 h-16 mx-auto mb-4 text-orange-500" />
              <h2 className="text-3xl font-bold mb-2">
                {isLogin ? 'Entrar' : 'Criar Conta'}
              </h2>
              <p className="text-gray-400">
                {isLogin ? 'Acesse sua conta PapoFire' : 'Junte-se ao PapoFire'}
              </p>
            </div>

            {/* Tabs Login/Register */}
            <div className="flex bg-white/5 rounded-xl p-1 mb-6">
              <button
                onClick={() => {
                  setIsLogin(true);
                }}
                className={`flex-1 py-3 rounded-lg font-medium transition-all ${
                  isLogin 
                    ? 'bg-orange-500 text-white' 
                    : 'text-gray-400 hover:text-white'
                }`}
              >
                Entrar
              </button>
              <button
                onClick={() => {
                  setIsLogin(false);
                }}
                className={`flex-1 py-3 rounded-lg font-medium transition-all ${
                  !isLogin 
                    ? 'bg-orange-500 text-white' 
                    : 'text-gray-400 hover:text-white'
                }`}
              >
                Cadastrar
              </button>
            </div>
            <div className="space-y-4">
              {/* Seletor de Gênero para cadastro */}
              {!isLogin && (
                <div className="mb-6">
                  <label className="block text-sm font-medium text-gray-300 mb-3">Seu gênero</label>
                  <div className="flex gap-2">
                    <button 
                      onClick={()=> setGender('male')} 
                      className={`flex-1 px-4 py-3 rounded-xl font-medium transition-all ${
                        gender === 'male' 
                          ? 'bg-blue-500 text-white' 
                          : 'bg-gray-700/50 text-gray-300 hover:bg-gray-600/50'
                      }`}
                    >
                      👨 Homem
                    </button>
                    <button 
                      onClick={() => setGender('female')} 
                      className={`flex-1 px-4 py-3 rounded-xl font-medium transition-all ${
                        gender === 'female' 
                          ? 'bg-pink-500 text-white' 
                          : 'bg-gray-700/50 text-gray-300 hover:bg-gray-600/50'
                      }`}
                    >
                      👩 Mulher
                    </button>
                  </div>
                </div>
              )}
              <div className="relative">
                <Mail className="w-5 h-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                <input 
                  ref={emailRef}
                  type="email" 
                  placeholder="Seu melhor e-mail" 
                  value={email} 
                  onChange={(e) => setEmail(e.target.value)}
                  onKeyDown={(e) => {
                    if (e.key === 'Enter') {
                      e.preventDefault();
                      handleAuth();
                    }
                  }}
                  className="w-full pl-12 pr-4 py-4 bg-white/10 rounded-xl border border-white/20 focus:border-orange-500 focus:outline-none focus:ring-2 focus:ring-orange-500/20 transition-all"
                  {...inputConfig}
                />
              </div>
              <div className="relative">
                <Lock className="w-5 h-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                <input 
                  ref={passwordRef}
                  type={showPassword ? 'text' : 'password'} 
                  placeholder="Senha segura" 
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  onKeyDown={(e) => {
                    if (e.key === 'Enter') {
                      e.preventDefault();
                      handleAuth();
                    }
                  }}
                  className="w-full pl-12 pr-12 py-4 bg-white/10 rounded-xl border border-white/20 focus:border-orange-500 focus:outline-none focus:ring-2 focus:ring-orange-500/20 transition-all"
                  {...inputConfig}
                />
                <button 
                  type="button"
                  onClick={() => setShowPassword(!showPassword)}
                  className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-white transition-colors"
                >
                  {showPassword ? <EyeOff className="w-5 h-5" /> : <Eye className="w-5 h-5" />}
                </button>
              </div>
              {/* Confirmar Senha só no registro */}
              {!isLogin && (
                <div className="relative">
                  <Lock className="w-5 h-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                  <input 
                    ref={confirmPasswordRef}
                    type={showPassword ? 'text' : 'password'} 
                    placeholder="Confirme sua senha" 
                    value={confirmPassword}
                    onChange={(e) => setConfirmPassword(e.target.value)}
                    onKeyDown={(e) => {
                      if (e.key === 'Enter') {
                        e.preventDefault();
                        handleAuth();
                      }
                    }}
                    className="w-full pl-12 pr-4 py-4 bg-white/10 rounded-xl border border-white/20 focus:border-orange-500 focus:outline-none focus:ring-2 focus:ring-orange-500/20 transition-all"
                    {...inputConfig}
                  />
                </div>
              )}
              {/* Mensagem de erro */}
              <AnimatePresence>
                {authError && (
                  <motion.div
                    initial={{ opacity: 0, y: -10 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -10 }}
                    className="bg-red-500/20 border border-red-500/30 rounded-xl p-3 text-red-400 text-sm text-center"
                  >
                    {authError}
                  </motion.div>
                )}
              </AnimatePresence>
            </div>
            <button 
              onClick={handleAuth} 
              className="w-full mt-8 py-4 text-lg bg-gradient-to-r from-orange-500 to-red-600 text-white rounded-lg font-bold"
              disabled={!email.trim() || !password.trim()}
            >
              {isLogin ? 'Entrar no PapoFire' : 'Criar Conta Grátis'}
            </button>
            <div className="text-center mt-6">
              <button 
                onClick={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                  setIsLogin(!isLogin);
                }} 
                className="text-gray-400 hover:text-orange-400 transition-colors"
              >
                {isLogin ? 'Não tem conta? Criar agora' : 'Já tem conta? Entrar'}
              </button>
            </div>
            {/* Botão Fechar */}
            <button 
              onClick={onClose} 
              className="absolute top-4 right-4 p-2 rounded-full hover:bg-white/10 transition-colors"
            >
              <X className="w-5 h-5" />
            </button>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default AuthModal; 