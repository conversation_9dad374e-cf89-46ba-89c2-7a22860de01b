// CONFIGURE:
// 1. Crie um projeto em https://app.supabase.com/
// 2. Copie a URL do projeto e a chave anon pública
// 3. Substitua as variáveis abaixo
// 4. Use o client supabase em qualquer lugar do app

import { createClient } from '@supabase/supabase-js';

const supabaseUrl = 'https://uqsbkjnxgpmufbuxpfve.supabase.co';
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InVxc2Jram54Z3BtdWZidXhwZnZlIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTMxMzUxMjcsImV4cCI6MjA2ODcxMTEyN30.fApVzhGIq1DoK8pULLT-xumiM-35R0jCNnDCIZ24cEg';

export const supabase = createClient(supabaseUrl, supabaseAnonKey);

// --- Exemplos de uso ---
// Autenticação
// await supabase.auth.signUp({ email, password });
// await supabase.auth.signInWithPassword({ email, password });
// await supabase.auth.signOut();

// CRUD Conversas
// await supabase.from('conversations').insert([{ user_id, title }]);
// await supabase.from('conversations').select('*').eq('user_id', user_id);

// CRUD Mensagens
// await supabase.from('messages').insert([{ conversation_id, user_id, content }]);
// await supabase.from('messages').select('*').eq('conversation_id', conversation_id);

// --- Exemplo de hook React para buscar conversas ---
// import { useEffect, useState } from 'react';
// function useConversations(user_id) {
//   const [data, setData] = useState([]);
//   useEffect(() => {
//     supabase.from('conversations').select('*').eq('user_id', user_id)
//       .then(({ data }) => setData(data || []));
//   }, [user_id]);
//   return data;
// } 