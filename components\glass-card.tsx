import React, { useState } from 'react';
import { motion } from 'framer-motion';

interface GlassCardProps {
  children: React.ReactNode;
  className?: string;
  onClick?: () => void;
  variant?: 'default' | 'premium' | 'neon' | 'holographic';
  size?: 'sm' | 'md' | 'lg' | 'xl';
  glow?: boolean;
  floating?: boolean;
}

export const GlassCard: React.FC<GlassCardProps> = ({ 
  children, 
  className = '', 
  onClick,
  variant = 'default',
  size = 'md',
  glow = false,
  floating = false
}) => {
  const [isHovered, setIsHovered] = useState(false);

  const variants = {
    default: 'bg-white/10 border-white/20',
    premium: 'bg-gradient-to-br from-orange-500/20 to-red-500/20 border-orange-500/30',
    neon: 'bg-gradient-to-br from-cyan-500/20 to-purple-500/20 border-cyan-500/30',
    holographic: 'bg-gradient-to-br from-pink-500/20 via-purple-500/20 to-cyan-500/20 border-pink-500/30'
  };

  const sizes = {
    sm: 'p-4 rounded-xl',
    md: 'p-6 rounded-2xl',
    lg: 'p-8 rounded-3xl',
    xl: 'p-10 rounded-3xl'
  };

  const glowEffect = glow ? 'shadow-2xl shadow-orange-500/25' : '';
  const floatingEffect = floating ? 'hover:shadow-2xl hover:shadow-orange-500/30' : '';

  return (
    <motion.div
      className={`
        ${variants[variant]} 
        ${sizes[size]}
        ${glowEffect}
        ${floatingEffect}
        backdrop-blur-xl border relative overflow-hidden cursor-pointer
        transition-all duration-500 ease-out
        ${className}
      `}
      initial={{ opacity: 0, y: 20, rotateX: 10 }}
      animate={{ 
        opacity: 1, 
        y: 0, 
        rotateX: 0,
        ...(floating && { y: isHovered ? -10 : 0 })
      }}
      whileHover={{ 
        scale: onClick ? 1.02 : 1.01,
        rotateY: 2,
        rotateX: -2,
        z: 50
      }}
      whileTap={{ scale: onClick ? 0.98 : 1 }}
      transition={{ 
        type: "spring", 
        stiffness: 300, 
        damping: 30,
        duration: 0.3
      }}
      style={{
        transformStyle: 'preserve-3d',
        perspective: '1000px'
      }}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      onClick={onClick}
    >
      {/* Holographic overlay */}
      <motion.div
        className="absolute inset-0 opacity-0 bg-gradient-to-r from-transparent via-white/10 to-transparent"
        animate={{
          opacity: isHovered ? [0, 0.3, 0] : 0,
          x: isHovered ? [-100, 300] : -100
        }}
        transition={{ duration: 1.5, ease: "easeInOut" }}
      />
      
      {/* Animated border glow */}
      {variant === 'premium' && (
        <motion.div
          className="absolute inset-0 rounded-inherit"
          style={{
            background: 'linear-gradient(45deg, transparent, rgba(255,165,0,0.3), transparent)',
            filter: 'blur(1px)'
          }}
          animate={{
            rotate: isHovered ? 360 : 0
          }}
          transition={{ duration: 3, ease: "linear", repeat: isHovered ? Infinity : 0 }}
        />
      )}

      {/* Content with 3D transform */}
      <motion.div
        style={{
          transform: isHovered ? 'translateZ(20px)' : 'translateZ(0px)',
          transformStyle: 'preserve-3d'
        }}
        transition={{ duration: 0.3 }}
      >
        {children}
      </motion.div>

      {/* Floating particles effect */}
      {variant === 'holographic' && isHovered && (
        <div className="absolute inset-0 pointer-events-none">
          {[...Array(6)].map((_, i) => (
            <motion.div
              key={i}
              className="absolute w-1 h-1 bg-cyan-400 rounded-full"
              initial={{
                x: Math.random() * 100 + '%',
                y: '100%',
                opacity: 0
              }}
              animate={{
                y: '-10%',
                opacity: [0, 1, 0]
              }}
              transition={{
                duration: 2,
                delay: i * 0.2,
                repeat: Infinity
              }}
            />
          ))}
        </div>
      )}
    </motion.div>
  );
};

export default GlassCard;
