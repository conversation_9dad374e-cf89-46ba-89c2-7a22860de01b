import { useEffect, useState } from 'react';
import { supabase } from '../lib/supabaseClient';

export function useMessages(conversation_id: string | null) {
  const [messages, setMessages] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (!conversation_id) return;
    setLoading(true);
    const fetch = async () => {
      const { data, error } = await supabase
        .from('messages')
        .select('*')
        .eq('conversation_id', conversation_id)
        .order('created_at', { ascending: true });
      setMessages(data || []);
      setError(error ? error.message : null);
      setLoading(false);
    };
    fetch();

    // Realtime subscription
    const channel = supabase
      .channel('messages')
      .on('postgres_changes', { event: '*', schema: 'public', table: 'messages', filter: `conversation_id=eq.${conversation_id}` }, (payload) => {
        fetch();
      })
      .subscribe();

    return () => { supabase.removeChannel(channel); };
  }, [conversation_id]);

  // Função para enviar mensagem
  const sendMessage = async (user_id: string, content: string) => {
    const { data, error } = await supabase
      .from('messages')
      .insert([{ conversation_id, user_id, content }])
      .select();
    if (data) setMessages((prev) => [...prev, data[0]]);
    if (error) setError(error.message);
    return { data, error };
  };

  return { messages, loading, error, sendMessage };
} 