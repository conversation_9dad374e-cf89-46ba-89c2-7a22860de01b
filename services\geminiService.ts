
import { GoogleGenAI, GenerateContentResponse } from "@google/genai";
import { GeneratorMode, type GenerationResult, type SocialPlatformId, type ToneId, type Gender, type UploadedFile } from '../types';
import { SOCIAL_PLATFORMS } from "../constants";

// In a production environment, this should be handled securely, for example, via environment variables.
const API_KEY = process.env.API_KEY || process.env.GEMINI_API_KEY;
if (!API_KEY) {
    console.warn("API_KEY not found, using fallback responses");
}

const ai = API_KEY ? new GoogleGenAI({ apiKey: API_KEY }) : null;

const buildPrompt = (
  platformId: SocialPlatformId,
  toneId: ToneId,
  gender: Gender | undefined,
  topic: string,
  mode: GeneratorMode
): string => {
  const platform = SOCIAL_PLATFORMS.find(p => p.id === platformId);
  if (!platform) throw new Error("Invalid platform");
  if (!gender) throw new Error("Gender is required for generation.");


  const genderContext = gender === 'male'
    ? 'A IA deve gerar mensagens na perspectiva de um homem brasileiro respondendo a uma mulher de forma romântica e direta.'
    : 'A IA deve gerar mensagens na perspectiva de uma mulher brasileira respondendo a um homem de forma romântica e direta.';

  const tomInstructions: { [key in ToneId | string]: string } = {
    casual: 'Tom descontraído, mas com um toque de interesse. Curto e direto.',
    flerte: 'Charme, inteligência e um pingo de ousadia. Crie uma faísca. Use elogios espertos e convites implícitos. Direto ao ponto, sem rodeios.',
    fofo: 'Seja genuinamente carinhoso e doce, mas sem ser infantil. Mensagens curtas que aquecem o coração.',
    confiante: 'Mostre segurança e personalidade forte, de forma atraente e magnética. Frases de impacto.',
    romantico_sutil: 'Afeto e carinho de forma elegante e não-óbvia. Uma frase que faz pensar. Curto, poético e impactante.',
  };

  const genderInstructions = gender === 'male'
    ? `PAPEL E DIRETRIZ: Você é um homem brasileiro: charmoso, direto e confiante. Suas respostas são para uma mulher. Use gírias masculinas modernas (ex: "e aí", "demorou", "fechou"). SEJA DIRETO E ROMÂNTICO. Proibido usar "linda", "gata", "princesa". Elogie a personalidade, o sorriso, a inteligência dela. Crie tensão e curiosidade.`
    : `PAPEL E DIRETRIZ: Você é uma mulher brasileira: divertida, charmosa e segura de si. Suas respostas são para um homem. Use termos carinhosos se o tom permitir (ex: "bem", "querido"), mas com confiança. SEJA DIRETA E ROMÂNTICA. Crie uma conexão real, seja envolvente e um pouco misteriosa.`;
  
  let promptTopic = ``;
  switch (mode) {
      case GeneratorMode.Story:
          promptTopic = `O print do story mostra: ${topic}. Gere uma resposta para este story.`;
          break;
      case GeneratorMode.Chat:
          promptTopic = `O print da conversa mostra o contexto: ${topic}. Gere uma resposta para continuar a conversa.`;
          break;
      case GeneratorMode.Compliment:
          promptTopic = `Quero elogiar isso: "${topic}". Crie um elogio específico e charmoso.`;
          break;
      case GeneratorMode.DateIdea:
          promptTopic = `Quero ideias de encontro com esta vibe: "${topic}". Sugira encontros criativos e românticos.`;
          break;
      case GeneratorMode.Topic:
      default:
          promptTopic = `Tópico da conversa: "${topic}"`;
          break;
  }

  return `OBJETIVO PRINCIPAL: Gerar respostas curtas, diretas, e MUITO ROMÂNTICAS. SEM ENROLAÇÃO, SEM "BLÁ BLÁ BLÁ". CADA PALAVRA CONTA.

${genderContext}
${genderInstructions}

=== CONTEXTO DA MENSAGEM ===
- Plataforma: ${platform.label} (Max: ${platform.chars} chars)
- Tom Desejado: ${toneId.toUpperCase()}
- Instrução de Tom: "${tomInstructions[toneId]}"
- ${promptTopic}

=== REGRAS DE OURO (NÃO QUEBRE!) ===
1.  **DIRETO AO PONTO**: Respostas curtas e impactantes. Sem frases de enchimento.
2.  **ZERO CLICHÊ**: Esqueça frases prontas. Seja original e autêntico.
3.  **ROMANCE INTELIGENTE**: Mostre interesse genuíno, não desespero.
4.  **HUMANO, NÃO ROBÔ**: Use linguagem 100% natural do Brasil.
5.  **GÍRIAS NA MEDIDA CERTA**: Use gírias do gênero selecionado com naturalidade.
6.  **RESPEITE O LIMITE DE CARACTERES**: NUNCA ultrapasse ${platform.chars} caracteres.

Gere EXATAMENTE 3 a 5 opções de mensagem. Cada opção deve ser separada por "---".
`;
};

const fileToGenerativePart = async (file: File) => {
  const base64EncodedDataPromise = new Promise<string>((resolve) => {
    const reader = new FileReader();
    reader.onloadend = () => resolve((reader.result as string).split(',')[1]);
    reader.readAsDataURL(file);
  });
  return {
    inlineData: { data: await base64EncodedDataPromise, mimeType: file.type },
  };
};

export const generateIdeas = async (
  platformId: SocialPlatformId,
  toneId: ToneId,
  gender: Gender | undefined,
  topic: string,
  mode: GeneratorMode
): Promise<GenerationResult[]> => {
  if (!gender) {
    return [{id: 'error', text: 'Erro: Gênero não definido. Por favor, selecione seu gênero.'}];
  }
  const prompt = buildPrompt(platformId, toneId, gender, topic, mode);
  
  if (!ai) {
    // Fallback responses when API key is not available
    return generateFallbackResponses(topic, toneId, gender);
  }

  try {
    const response: GenerateContentResponse = await ai.models.generateContent({
        model: "gemini-2.5-flash",
        contents: [{ parts: [{ text: prompt }] }],
    });

    const responseText = response.text;
    return responseText.split('---').map(idea => idea.trim()).filter(idea => idea.length > 0).map(idea => ({ id: crypto.randomUUID(), text: idea }));
  } catch (error) {
    console.error("Gemini API error:", error);
    return generateFallbackResponses(topic, toneId, gender);
  }
};

export const generateIdeasFromImage = async (
  platformId: SocialPlatformId,
  toneId: ToneId,
  gender: Gender | undefined,
  imageFile: UploadedFile,
  mode: GeneratorMode.Story | GeneratorMode.Chat,
  userIntent?: string
): Promise<GenerationResult[]> => {
  if (!gender) {
    return [{id: 'error', text: 'Erro: Gênero não definido. Por favor, selecione seu gênero.'}];
  }
  const imagePart = await fileToGenerativePart(imageFile.file);
  const platform = SOCIAL_PLATFORMS.find(p => p.id === platformId);
  if (!platform) throw new Error("Invalid platform");

  const genderInstructions = gender === 'male'
    ? `PAPEL E DIRETRIZ: Você é um homem brasileiro: charmoso, direto e confiante. Suas respostas são para uma mulher. Use gírias masculinas modernas (ex: "e aí", "demorou", "fechou"). SEJA DIRETO E ROMÂNTICO. Proibido usar "linda", "gata", "princesa". Elogie a personalidade, o sorriso, a inteligência dela. Crie tensão e curiosidade.`
    : `PAPEL E DIRETRIZ: Você é uma mulher brasileira: divertida, charmosa e segura de si. Suas respostas são para um homem. Use termos carinhosos se o tom permitir (ex: "bem", "querido"), mas com confiança. SEJA DIRETA E ROMÂNTICA. Crie uma conexão real, seja envolvente e um pouco misteriosa.`;
    
  const tomInstructions: { [key in ToneId | string]: string } = {
    casual: 'Tom descontraído, mas com um toque de interesse. Curto e direto.',
    flerte: 'Charme, inteligência e um pingo de ousadia. Crie uma faísca. Use elogios espertos e convites implícitos. Direto ao ponto, sem rodeios.',
    fofo: 'Seja genuinamente carinhoso e doce, mas sem ser infantil. Mensagens curtas que aquecem o coração.',
    confiante: 'Mostre segurança e personalidade forte, de forma atraente e magnética. Frases de impacto.',
    romantico_sutil: 'Afeto e carinho de forma elegante e não-óbvia. Uma frase que faz pensar. Curto, poético e impactante.',
  };
    
  let prompt;

  if (mode === GeneratorMode.Chat) {
    prompt = `OBJETIVO PRINCIPAL: Você é um assistente de conversas para o app PapoFire. Sua tarefa é analisar uma captura de tela de uma conversa (WhatsApp, Instagram, etc.) e gerar respostas humanizadas e inteligentes para dar continuidade ao diálogo.

${genderInstructions}

=== CONTEXTO DA MENSAGEM ===
- Plataforma Alvo: ${platform.label} (Max: ${platform.chars} caracteres)
- Tom Desejado: ${toneId.toUpperCase()} - ${tomInstructions[toneId]}
- Intenção do Usuário (opcional): ${userIntent || 'Continuar a conversa de forma natural e interessante.'}
- Captura de tela da conversa: [A imagem está sendo enviada junto com este prompt]

=== SUA TAREFA EM PASSOS ===
1.  **ANALISE A IMAGEM**: Leia a captura de tela. Identifique quem disse o quê, o tópico da conversa e, mais importante, a ÚLTIMA MENSAGEM que precisa de uma resposta.
2.  **ENTENDA O CONTEXTO**: Use o tom da conversa, a plataforma e a intenção do usuário para guiar suas sugestões.
3.  **GERE RESPOSTAS**: Crie de 3 a 5 opções de resposta para continuar a conversa.

=== REGRAS DE OURO (NÃO QUEBRE!) ===
1.  **CONTINUIDADE**: Suas respostas devem fazer sentido como a PRÓXIMA mensagem na conversa. Não comece um novo assunto do nada, a menos que a intenção do usuário peça isso.
2.  **NATURALIDADE**: Use linguagem 100% natural do Brasil, como uma pessoa real. Use as gírias apropriadas para o gênero selecionado com naturalidade.
3.  **SEJA DIRETO E ENVOLVENTE**: Respostas curtas, impactantes e que incentivem uma resposta da outra pessoa. Evite clichês.
4.  **RESPEITE O LIMITE DE CARACTERES**: NUNCA ultrapasse ${platform.chars} caracteres.
5.  **SEM DESCRIÇÃO, SÓ RESPOSTA**: Não descreva a imagem para mim. Apenas gere as opções de resposta.
6.  **ERRO DE LEITURA**: Se a imagem for ilegível ou não for uma conversa, responda apenas com o texto: "ERRO_IMAGEM_ILEGIVEL".

Gere as opções de resposta. Cada opção deve ser separada por "---".`;
  } else { // GeneratorMode.Story
    prompt = `OBJETIVO: Gerar uma resposta curta e inteligente para um story do Instagram ou outra rede social.

${genderInstructions}

=== CONTEXTO DA MENSAGEM ===
- Plataforma Alvo: ${platform.label} (Max: ${platform.chars} caracteres)
- Tom Desejado: ${toneId.toUpperCase()} - ${tomInstructions[toneId]}
- Conteúdo do Story: [A imagem está sendo enviada junto com este prompt]

=== SUA TAREFA ===
1.  **ANALISE A IMAGEM**: Entenda o conteúdo do story (foto, vídeo, texto).
2.  **GERAR RESPOSTA**: Crie uma resposta criativa, que puxe assunto e se relacione com o story.
3.  **REGRAS**: Siga as regras de tom, limite de caracteres e naturalidade.

Gere de 3 a 5 opções de resposta. Cada opção deve ser separada por "---".`;
  }

  try {
    const response: GenerateContentResponse = await ai.models.generateContent({
        model: "gemini-2.5-flash",
        contents: [{ parts: [imagePart, { text: prompt }] }],
    });

    const responseText = response.text;
    
    if (responseText.includes("ERRO_IMAGEM_ILEGIVEL")) {
        return [{ id: 'error', text: 'Não consegui ler o print. Tente enviar uma imagem mais nítida.' }];
    }

    return responseText.split('---').map(idea => idea.trim()).filter(idea => idea.length > 0).map(idea => ({ id: crypto.randomUUID(), text: idea }));

  } catch (error) {
    console.error("Gemini Image Analysis Error:", error);
     return [
      { id: '1', text: "Uau, que legal! Me conta mais sobre isso." },
      { id: '2', text: "Adorei a vibe! Onde foi isso?" },
      { id: '3', text: "Isso parece muito divertido!" }
    ];
  }
};

// Função para gerar respostas de fallback quando a API não está disponível
const generateFallbackResponses = (topic: string, toneId: ToneId, gender: Gender): GenerationResult[] => {
  const responses = {
    casual: [
      `E aí, ${topic}! Conta mais sobre isso.`,
      `Legal esse papo de ${topic}! Como foi?`,
      `${topic}? Interessante! Me fala mais.`
    ],
    flerte: [
      `${topic}... você tem bom gosto! 😏`,
      `Adorei esse assunto de ${topic}. Você é interessante.`,
      `${topic}? Agora fiquei curiosa sobre você.`
    ],
    fofo: [
      `Que fofo você gostar de ${topic}! ❤️`,
      `${topic} é uma das minhas coisas favoritas também!`,
      `Você falando de ${topic} deixou meu dia melhor.`
    ],
    confiante: [
      `${topic}? Eu domino esse assunto.`,
      `Interessante... ${topic} diz muito sobre você.`,
      `${topic} é só o começo. Tenho muito mais para te contar.`
    ],
    romantico_sutil: [
      `${topic}... como você consegue ser tão interessante?`,
      `Falando de ${topic}, você me lembra de algo especial.`,
      `${topic} nunca foi tão interessante quanto agora.`
    ]
  };

  const selectedResponses = responses[toneId] || responses.casual;
  return selectedResponses.map((text, index) => ({ 
    id: `fallback-${index}`, 
    text 
  }));
};
