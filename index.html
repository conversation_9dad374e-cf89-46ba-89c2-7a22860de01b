<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/png" href="/assets/fire-logo.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>PapoFire - AI Conversation Generator</title>
    <link rel="stylesheet" href="https://rsms.me/inter/inter.css">
  <script type="importmap">
{
  "imports": {
    "react": "https://esm.sh/react@^19.1.0",
    "react-dom/": "https://esm.sh/react-dom@^19.1.0/",
    "react/": "https://esm.sh/react@^19.1.0/",
    "lucide-react": "https://esm.sh/lucide-react@^0.525.0",
    "@google/genai": "https://esm.sh/@google/genai@^1.9.0",
    "framer-motion": "https://esm.sh/framer-motion@^12.23.6",
    "clsx": "https://esm.sh/clsx@^2.1.1",
    "tailwind-merge": "https://esm.sh/tailwind-merge@^2.4.0"
  }
}
</script>
<link rel="stylesheet" href="/index.css">
</head>
  <body>
    <div id="root"></div>
    <script type="module" src="/index.tsx"></script>
  </body>
</html>