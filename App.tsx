import React, { useState, useEffect, useRef, useCallback, useMemo } from 'react';
import { motion, AnimatePresence, useMotionValue, useSpring, useTransform } from 'framer-motion';
import { ChevronLeft, MessageCircle, Camera, Zap, Heart, Star, Copy, Check, Sparkles, Flame, ChevronDown, Menu, X, Crown, Rocket, Shield, Infinity as InfinityIcon, Brain, Search, Globe, Users, User, Lightbulb, Target, Database, Cpu, Monitor, Upload, Send, Loader2, FileUp, Link, ExternalLink, ArrowRight, ArrowLeft, TrendingUp, BarChart3, Eye, Layers, Bookmark, Hash, Wifi, WifiOff, Cloud, Download, Pause, Play, RotateCcw, Share2, Plus, Minus, Volume2, VolumeX, Maximize, Minimize, Settings, Filter, SortAsc, AlignLeft, Type, Image, Video, Music, FileText, Folder, Clock, Calendar, MapPin, Phone, Mail, Home, Building, Car, Plane, Ship, Train, Bike, Zap as Lightning, Sun, Moon, Star as StarIcon, Cloud as CloudIcon, Droplets, Snowflake, Wind, Thermometer, Compass, Navigation, Route, Flag, Award, Trophy, Medal, Gift, ShoppingBag, CreditCard, DollarSign, Euro, PoundSterling, Bitcoin, Smartphone, Laptop, Tablet, Watch, Headphones, Speaker, Mic, Camera as CameraIcon, Video as VideoIcon, Tv, Radio, Gamepad2, Joystick, Dices, Box, Brush, Palette, Paintbrush, Scissors, Ruler, PenTool, Edit3, FileEdit, Save, FolderOpen, Archive, Trash2, Delete, RefreshCw, RotateCw, FlipHorizontal, FlipVertical, Crop, ZoomIn, ZoomOut, Move, Expand, Shuffle, ThumbsUp, ThumbsDown, Timer, Mic2, Sliders, EyeOff, Lock } from 'lucide-react';
import AuthModal from './components/AuthModal';
import { LandingPage } from './pages/landing/LandingPage';
import HomePage from './pages/home/<USER>';
import { AuthProvider, useAuth } from './hooks/useAuth';
import { ProtectedRoute } from './components/ProtectedRoute';
import { AuthForm } from './components/AuthForm';
import ChatPage from './pages/chat/index';
import { createClient } from '@supabase/supabase-js'
import { supabase } from './lib/supabaseClient';

// 🔥 PapoFire 3.0 - MULTI-PLATFORM ULTRA-HUMANIZED CONVERSATION GENERATOR 🔥
// NOVA VERSÃO: Sistema inteligente por plataforma com IA otimizada para cada rede social

// Social Platform Selector Component - MULTI-PLATAFORMA!
const SocialPlatformSelector = ({ value, onChange }: { value: string, onChange: (value: string) => void }) => {
  const platforms = [
    { id: 'whatsapp', label: 'WhatsApp', icon: '💬', color: 'from-green-500 to-green-600', chars: '≤160' },
    { id: 'instagram', label: 'Instagram', icon: '📸', color: 'from-pink-500 to-purple-600', chars: '≤100' },
    { id: 'tiktok', label: 'TikTok', icon: '🎵', color: 'from-black to-pink-500', chars: '≤150' },
    { id: 'twitter', label: 'Twitter', icon: '🐦', color: 'from-blue-400 to-blue-600', chars: '≤280' },
  ];

  return (
    <div className="space-y-3">
      <label className="block text-sm font-medium text-gray-300 flex items-center gap-2">
        <Globe className="w-4 h-4" />
        Plataforma Social
      </label>
      <div className="grid grid-cols-2 gap-2">
        {platforms.map(platform => (
          <motion.button
            key={platform.id}
            onClick={() => onChange(platform.id)}
            className={`px-3 py-3 rounded-xl text-xs flex items-center gap-2 transition-all border ${
              value === platform.id 
                ? `bg-gradient-to-r ${platform.color} text-white border-white/20 shadow-lg` 
                : 'bg-gray-800/50 text-gray-400 border-gray-700/50 hover:bg-gray-700/50'
            }`}
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
          >
            <span className="text-lg">{platform.icon}</span>
            <div className="text-left">
              <div className="font-medium">{platform.label}</div>
              <div className="text-xs opacity-80">{platform.chars}</div>
            </div>
          </motion.button>
        ))}
      </div>
    </div>
  );
};

// Tom da Conversa Selector - OTIMIZADO POR PLATAFORMA!
const TomSelector = ({ value, onChange }: { value: string, onChange: (value: string) => void }) => {
  const tons = [
    { id: 'casual', label: 'Casual', icon: '😊', color: 'from-blue-500 to-cyan-500' },
    { id: 'flerte', label: 'Flerte', icon: '😏', color: 'from-purple-500 to-pink-500' },
    { id: 'fofo', label: 'Fofo', icon: '🥺', color: 'from-pink-500 to-rose-500' },
    { id: 'confiante', label: 'Confiante', icon: '😎', color: 'from-orange-500 to-red-500' },
  ];

  return (
    <div className="space-y-3">
      <label className="block text-sm font-medium text-gray-300 flex items-center gap-2">
        <Heart className="w-4 h-4" />
        Tom da Conversa
      </label>
      <div className="grid grid-cols-2 gap-2">
        {tons.map(tom => (
          <motion.button
            key={tom.id}
            onClick={() => onChange(tom.id)}
            className={`px-3 py-2 rounded-xl text-xs flex items-center gap-2 transition-all border ${
              value === tom.id 
                ? `bg-gradient-to-r ${tom.color} text-white border-white/20 shadow-lg` 
                : 'bg-gray-800/50 text-gray-400 border-gray-700/50 hover:bg-gray-700/50'
            }`}
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
          >
            <span className="text-lg">{tom.icon}</span>
            <div className="font-medium">{tom.label}</div>
          </motion.button>
        ))}
      </div>
    </div>
  );
};

// Roleta de Temas Component
const RoletaTemas = ({ onResult }: { onResult: (theme: any) => void }) => {
  const [spinning, setSpinning] = useState(false);
  const [rotation, setRotation] = useState(0);
  
  const temas = [
    { id: 'viagem', label: 'Viagem', emoji: '✈️', color: '#3B82F6' },
    { id: 'comida', label: 'Comida', emoji: '🍕', color: '#EF4444' },
    { id: 'música', label: 'Música', emoji: '🎵', color: '#8B5CF6' },
    { id: 'meme', label: 'Meme', emoji: '😂', color: '#F59E0B' },
    { id: 'cantadas', label: 'Cantadas', emoji: '😏', color: '#EC4899' },
    { id: 'séries', label: 'Séries', emoji: '📺', color: '#10B981' },
    { id: 'esportes', label: 'Esportes', emoji: '⚽', color: '#F97316' },
    { id: 'festa', label: 'Festa', emoji: '🎉', color: '#06B6D4' },
  ];

  const spin = () => {
    if (spinning) return;
    
    setSpinning(true);
    const spins = 5 + Math.random() * 5; // 5-10 voltas
    const finalRotation = rotation + spins * 360 + Math.random() * 360;
    setRotation(finalRotation);
    
    setTimeout(() => {
      const index = Math.floor((finalRotation % 360) / (360 / temas.length));
      const selectedTema = temas[index];
      setSpinning(false);
      onResult(selectedTema);
    }, 3000);
  };

  return (
    <div className="flex flex-col items-center space-y-6">
      <div className="relative w-64 h-64">
        <motion.div
          className="w-full h-full rounded-full border-4 border-orange-500 relative overflow-hidden"
          style={{ 
            background: 'conic-gradient(' + temas.map((tema, i) => 
              `${tema.color} ${i * (360/temas.length)}deg ${(i+1) * (360/temas.length)}deg`
            ).join(', ') + ')'
          }}
          animate={{ rotate: rotation }}
          transition={{ duration: spinning ? 3 : 0, ease: spinning ? 'easeOut' : 'linear' }}
        >
          {temas.map((tema, i) => (
            <div
              key={tema.id}
              className="absolute w-1/2 h-1/2 flex items-center justify-center text-white font-bold text-xl"
              style={{
                transform: `rotate(${i * (360/temas.length)}deg) translateY(-50%)`,
                transformOrigin: '100% 100%',
                top: '50%',
                left: '50%'
              }}
            >
              <div style={{ transform: `rotate(${-(i * (360/temas.length))}deg)` }}>
                {tema.emoji}
              </div>
            </div>
          ))}
        </motion.div>
        
        {/* Ponteiro */}
        <div className="absolute top-0 left-1/2 transform -translate-x-1/2 -translate-y-2">
          <div className="w-0 h-0 border-l-4 border-r-4 border-b-8 border-l-transparent border-r-transparent border-b-white" />
        </div>
      </div>

      <GlowingButton
        onClick={spin}
        disabled={spinning}
        className="px-8 py-3"
      >
        {spinning ? (
          <span className="flex items-center gap-2">
            <Loader2 className="w-5 h-5 animate-spin" />
            Girando...
          </span>
        ) : (
          <span className="flex items-center gap-2">
            <Shuffle className="w-5 h-5" />
            Girar Roleta 🎰
          </span>
        )}
      </GlowingButton>
    </div>
  );
};

// Streak Tracker Component
const StreakTracker = ({ streak, gems }: { streak: number, gems: number }) => {
  const maxStreak = 7;
  const progress = (streak / maxStreak) * 100;

  return (
    <motion.div 
      className="bg-gradient-to-r from-orange-500/20 to-red-500/20 border border-orange-500/30 rounded-2xl p-4"
      animate={{ scale: [1, 1.02, 1] }}
      transition={{ duration: 2, repeat: Infinity }}
    >
      <div className="flex items-center justify-between mb-3">
        <div className="flex items-center gap-2">
          <Flame className="w-5 h-5 text-orange-500" />
          <span className="font-bold text-orange-400">Streak</span>
        </div>
        <div className="flex items-center gap-2">
          <Diamond className="w-4 h-4 text-cyan-400" />
          <span className="text-cyan-400 font-bold">{gems}</span>
        </div>
      </div>
      
      <div className="flex items-center gap-2 mb-3">
        {Array.from({ length: maxStreak }, (_, i) => (
          <motion.div
            key={i}
            className={`w-6 h-6 rounded-full flex items-center justify-center text-xs font-bold ${
              i < streak ? 'bg-orange-500 text-white' : 'bg-gray-700 text-gray-400'
            }`}
            animate={i < streak ? { scale: [1, 1.2, 1] } : {}}
            transition={{ delay: i * 0.1, duration: 0.5 }}
          >
            🔥
          </motion.div>
        ))}
      </div>
      
      <div className="text-center">
        <div className="text-sm text-gray-300">
          {streak} dias seguidos • {maxStreak - streak} para loot-box
        </div>
        <div className="w-full bg-gray-700 rounded-full h-2 mt-2">
          <div 
            className="bg-gradient-to-r from-orange-500 to-red-500 h-2 rounded-full transition-all"
            style={{ width: `${progress}%` }}
          />
        </div>
      </div>
    </motion.div>
  );
};

// Mic Button Component
const MicButton = ({ onTranscript, isListening }: { onTranscript: () => void, isListening: boolean }) => {
  return (
    <motion.button
      className={`w-16 h-16 rounded-full flex items-center justify-center transition-all ${
        isListening 
          ? 'bg-red-500 text-white shadow-lg' 
          : 'bg-gray-800 text-gray-400 hover:bg-gray-700'
      }`}
      whileHover={{ scale: 1.1 }}
      whileTap={{ scale: 0.9 }}
      animate={isListening ? { 
        boxShadow: [
          '0 0 0 0 rgba(239, 68, 68, 0.4)',
          '0 0 0 20px rgba(239, 68, 68, 0)',
        ]
      } : {}}
      transition={{ duration: 1, repeat: isListening ? Infinity : 0 }}
    >
      {isListening ? <VolumeX className="w-6 h-6" /> : <Mic2 className="w-6 h-6" />}
    </motion.button>
  );
};

// Vault Card Component + 3D Loot Box
const VaultCard = ({ phrase, tags, onLongPress }: { phrase: string, tags: string[], onLongPress: () => void }) => {
  const [isPressed, setIsPressed] = useState(false);
  
  const handleMouseDown = () => {
    setIsPressed(true);
    setTimeout(() => {
      if (isPressed) {
        onLongPress();
      }
    }, 800);
  };

  return (
    <motion.div
      className="bg-gray-800/50 border border-gray-700/50 rounded-xl p-4 cursor-pointer"
      onMouseDown={handleMouseDown}
      onMouseUp={() => setIsPressed(false)}
      onMouseLeave={() => setIsPressed(false)}
      whileHover={{ scale: 1.02 }}
      animate={isPressed ? { scale: 0.98 } : {}}
    >
      <p className="text-gray-200 mb-2">{phrase}</p>
      <div className="flex flex-wrap gap-1">
        {tags.map((tag: string, i: number) => (
          <span key={i} className="px-2 py-1 bg-blue-500/20 text-blue-300 rounded text-xs">
            #{tag}
          </span>
        ))}
      </div>
    </motion.div>
  );
};

// 3D Loot Box Component (Daily Rewards)
const LootBox3D = ({ onOpen, isOpen }: { onOpen: () => void, isOpen: boolean }) => {
  const [isRotating, setIsRotating] = useState(false);
  
  const handleClick = () => {
    if (!isOpen) {
      setIsRotating(true);
      setTimeout(() => {
        onOpen();
        setIsRotating(false);
      }, 2000);
    }
  };

  return (
    <motion.div
      className="w-24 h-24 mx-auto mb-4 cursor-pointer relative"
      onClick={handleClick}
      whileHover={{ scale: 1.1 }}
      whileTap={{ scale: 0.9 }}
    >
      <motion.div
        className="w-full h-full bg-gradient-to-br from-yellow-400 to-orange-500 rounded-xl shadow-2xl flex items-center justify-center"
        animate={isRotating ? { 
          rotateY: [0, 360],
          scale: [1, 1.2, 1],
          boxShadow: [
            '0 0 20px rgba(255, 193, 7, 0.5)',
            '0 0 40px rgba(255, 193, 7, 0.8)',
            '0 0 20px rgba(255, 193, 7, 0.5)'
          ]
        } : {}}
        transition={{ duration: 2, ease: 'easeInOut' }}
      >
        <motion.div
          animate={{ 
            rotate: isRotating ? 360 : 0,
            scale: isRotating ? [1, 1.5, 1] : 1
          }}
          transition={{ duration: 1, repeat: isRotating ? 2 : 0 }}
        >
          <Gift className="w-12 h-12 text-white" />
        </motion.div>
      </motion.div>
      
      {/* Sparkles Effect */}
      <AnimatePresence>
        {isRotating && (
          <motion.div
            className="absolute inset-0 pointer-events-none"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
          >
            {[...Array(8)].map((_, i) => (
              <motion.div
                key={i}
                className="absolute w-2 h-2 bg-yellow-300 rounded-full"
                style={{
                  left: '50%',
                  top: '50%',
                }}
                animate={{
                  x: [0, Math.cos(i * 45) * 50],
                  y: [0, Math.sin(i * 45) * 50],
                  scale: [1, 0],
                  opacity: [1, 0],
                }}
                transition={{
                  duration: 1,
                  delay: i * 0.1,
                  ease: 'easeOut'
                }}
              />
            ))}
          </motion.div>
        )}
      </AnimatePresence>
    </motion.div>
  );
};

// TrendMiner Component (DeepThink + Trending Topics)
const TrendMiner = ({ onTrendFound }: { onTrendFound: (trends: string[]) => void }) => {
  const [mining, setMining] = useState(false);
  const [foundTrends, setFoundTrends] = useState<string[]>([]);

  const mineCurrentTrends = async () => {
    setMining(true);
    try {
      // Simulate trending topics discovery
      const mockTrends = [
        'BBB 2025', 'Copa do Mundo', 'Carnaval Rio', 'Black Friday',
        'Dia dos Namorados', 'Festa Junina', 'Natal', 'Ano Novo'
      ];
      
      const selectedTrends = mockTrends.sort(() => 0.5 - Math.random()).slice(0, 3);
      setFoundTrends(selectedTrends);
      onTrendFound(selectedTrends);
      
    } catch (error) {
      console.error('Erro no TrendMiner:', error);
    } finally {
      setMining(false);
    }
  };

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h4 className="font-bold text-purple-400 flex items-center gap-2">
          <TrendingUp className="w-5 h-5" />
          TrendMiner
        </h4>
        <GlowingButton
          onClick={mineCurrentTrends}
          disabled={mining}
          variant="cyber"
          className="px-4 py-2 text-sm"
        >
          {mining ? (
            <motion.div
              animate={{ rotate: 360 }}
              transition={{ duration: 1, repeat: Infinity, ease: 'linear' }}
            >
              <Loader2 className="w-4 h-4" />
            </motion.div>
          ) : (
            <>
              <Search className="w-4 h-4 mr-2" />
              Minerar Trends
            </>
          )}
        </GlowingButton>
      </div>
      
      {foundTrends.length > 0 && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="grid gap-2"
        >
          {foundTrends.map((trend, index) => (
            <motion.button
              key={trend}
              onClick={() => onTrendFound([trend] as any)}
              className="px-3 py-2 bg-purple-500/20 text-purple-300 rounded-lg text-sm hover:bg-purple-500/30 transition-all text-left"
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: index * 0.1 }}
              whileHover={{ scale: 1.02 }}
            >
              🔥 {trend}
            </motion.button>
          ))}
        </motion.div>
      )}
    </div>
  );
};

// Frase Duel Component
const FraseDuel = ({ frases, onVote }: { frases: any[], onVote: (index: number) => void }) => {
  const [selectedIndex, setSelectedIndex] = useState<number | null>(null);
  
  return (
    <div className="space-y-4">
      <h3 className="text-center text-lg font-bold text-yellow-400">⚔️ Duelo de Frases</h3>
      <p className="text-center text-sm text-gray-400">Escolha a melhor frase:</p>
      
      <div className="grid grid-cols-1 gap-4">
        {frases.map((frase, index: number) => (
          <motion.button
            key={index}
            onClick={() => {
              setSelectedIndex(index);
              onVote(index);
            }}
            className={`p-4 rounded-xl border-2 transition-all text-left ${
              selectedIndex === index
                ? 'border-yellow-500 bg-yellow-500/20 text-yellow-300'
                : 'border-gray-700 bg-gray-800/50 text-gray-200 hover:border-gray-600'
            }`}
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
          >
            <div className="flex items-center justify-between">
              <span>{frase.text}</span>
              <div className="flex items-center gap-2">
                <ThumbsUp className="w-4 h-4" />
                <span className="text-sm">{frase.votes || 0}</span>
              </div>
            </div>
          </motion.button>
        ))}
      </div>
    </div>
  );
};

// Theme Toggle Component
const ThemeToggle = ({ theme, onThemeChange }: { theme: string, onThemeChange: (theme: string) => void }) => {
  const themes = [
    { id: 'dark', label: 'Dark', icon: '🌙', colors: 'from-gray-900 to-black' },
    { id: 'neon', label: 'Neon', icon: '⚡', colors: 'from-purple-900 to-cyan-900' },
    { id: 'retro', label: 'Retro', icon: '🕹️', colors: 'from-orange-900 to-pink-900' },
  ];

  return (
    <div className="flex gap-2">
      {themes.map(t => (
        <motion.button
          key={t.id}
          onClick={() => onThemeChange(t.id)}
          className={`px-3 py-2 rounded-lg text-sm flex items-center gap-2 transition-all ${
            theme === t.id 
              ? 'bg-orange-500 text-black' 
              : 'bg-gray-800 text-gray-400 hover:bg-gray-700'
          }`}
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
        >
          <span>{t.icon}</span>
          {t.label}
        </motion.button>
      ))}
    </div>
  );
};

// Diamond Icon Component
const Diamond = ({ className = '' }) => (
  <svg className={className} viewBox="0 0 24 24" fill="currentColor">
    <path d="M6,2L18,2L22,8L12,22L2,8L6,2Z" />
  </svg>
);

// Upload Progress Component
const UploadProgress = ({ progress, isVisible }: { progress: number, isVisible: boolean }) => {
  if (!isVisible) return null;

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      className="fixed top-4 right-4 bg-gray-900/95 backdrop-blur-md border border-white/20 rounded-xl p-4 min-w-[280px] z-50"
    >
      <div className="flex items-center gap-3 mb-3">
        <motion.div
          animate={{ rotate: 360 }}
          transition={{ duration: 2, repeat: Infinity, ease: 'linear' }}
        >
          <Upload className="w-5 h-5 text-blue-400" />
        </motion.div>
        <span className="text-sm font-medium text-white">Fazendo upload...</span>
      </div>
      
      <div className="w-full bg-gray-700 rounded-full h-2">
        <motion.div
          className="bg-gradient-to-r from-blue-500 to-purple-500 h-2 rounded-full"
          initial={{ width: 0 }}
          animate={{ width: `${progress}%` }}
          transition={{ duration: 0.3 }}
        />
      </div>
      
      <div className="text-xs text-gray-400 mt-2 text-center">
        {progress}% completo
      </div>
    </motion.div>
  );
};

// Upload Success Component
const UploadSuccess = ({ isVisible, onClose }: { isVisible: boolean, onClose: () => void }) => {
  useEffect(() => {
    if (isVisible) {
      const timer = setTimeout(() => {
        onClose();
      }, 3000);
      return () => clearTimeout(timer);
    }
  }, [isVisible, onClose]);

  if (!isVisible) return null;

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.8 }}
      animate={{ opacity: 1, scale: 1 }}
      exit={{ opacity: 0, scale: 0.8 }}
      className="fixed top-4 right-4 bg-green-500/20 backdrop-blur-md border border-green-500/50 rounded-xl p-4 min-w-[280px] z-50"
    >
      <div className="flex items-center gap-3">
        <motion.div
          initial={{ scale: 0 }}
          animate={{ scale: 1 }}
          transition={{ delay: 0.2 }}
        >
          <Check className="w-5 h-5 text-green-400" />
        </motion.div>
        <span className="text-sm font-medium text-green-400">Upload concluído!</span>
      </div>
      <div className="text-xs text-green-300 mt-1">
        Imagem processada com sucesso
      </div>
    </motion.div>
  );
};

// Enhanced UI Components - shadcn/ui inspired with all requested implementations

// Animated Shiny Text Component
const AnimatedShinyText = ({ children, className = '', speed = 3 }: { children: React.ReactNode, className?: string, speed?: number }) => (
  <motion.div
    className={`inline-block bg-gradient-to-r from-orange-400 via-red-500 via-purple-600 via-blue-500 to-cyan-400 bg-clip-text text-transparent bg-[length:400%_auto] ${className}`}
    animate={{
      backgroundPosition: ['0% 50%', '400% 50%', '0% 50%']
    }}
    transition={{
      duration: Math.max(speed || 3, 0.1),
      repeat: Infinity,
      ease: 'linear'
    }}
    style={{
      backgroundSize: '400% auto',
    }}
  >
    {children}
  </motion.div>
);

// Simplified Squares Background
const SquaresBackground = () => {
  const [squares, setSquares] = useState<{ id: number; left: number; top: number; delay: number; }[]>([]);

  useEffect(() => {
    try {
      const newSquares = Array.from({ length: 15 }, (_, i) => ({
        id: i,
        left: Math.random() * 100,
        top: Math.random() * 100,
        delay: i * 0.2,
      }));
      setSquares(newSquares);
    } catch (error) {
      console.error('Error creating squares:', error);
      setSquares([]);
    }
  }, []);

  if (squares.length === 0) {
    return null;
  }

  return (
    <div className="fixed inset-0 pointer-events-none z-0 opacity-10">
      {squares.map((square) => (
        <motion.div
          key={square.id}
          className="absolute w-1 h-1 bg-orange-500/40 rounded-sm"
          style={{
            left: `${square.left}%`,
            top: `${square.top}%`,
          }}
          animate={{
            opacity: [0.2, 0.8, 0.2],
            scale: [0.5, 1, 0.5],
          }}
          transition={{
            duration: 6,
            repeat: Infinity,
            delay: square.delay,
            ease: 'easeInOut',
          }}
        />
      ))}
    </div>
  );
};

// Glowing Button Component - FIXED for input handling
const GlowingButton = ({ children, onClick, variant = 'default', className = '', disabled = false, ...props }: { children: React.ReactNode, onClick?: (e: React.MouseEvent<HTMLButtonElement>) => void, variant?: string, className?: string, disabled?: boolean }) => {
  const baseClasses = 'relative overflow-hidden px-6 py-3 rounded-lg font-semibold transition-all duration-300 cursor-pointer disabled:opacity-50 disabled:cursor-not-allowed';
  const variants = {
    default: 'bg-gradient-to-r from-orange-500 to-red-600 text-white shadow-lg hover:shadow-orange-500/50',
    ghost: 'bg-transparent border-2 border-orange-500/50 text-orange-500 hover:bg-orange-500/10',
    premium: 'bg-gradient-to-r from-purple-500 to-pink-600 text-white shadow-lg hover:shadow-purple-500/50',
    cyber: 'bg-gradient-to-r from-cyan-500 to-blue-600 text-white shadow-lg hover:shadow-cyan-500/50',
    neon: 'bg-gradient-to-r from-green-400 to-emerald-600 text-white shadow-lg hover:shadow-green-500/50'
  };

  const handleClick = useCallback((e: React.MouseEvent<HTMLButtonElement>) => {
    if (disabled || !onClick) {
      e.preventDefault();
      return;
    }
    
    console.log('🔥 GlowingButton clicado!', { disabled, hasOnClick: !!onClick });
    onClick(e);
  }, [disabled, onClick]);

  return (
    <motion.button
      type="button"
      className={`${baseClasses} ${variants[variant as keyof typeof variants]} ${className}`}
      onClick={handleClick}
      disabled={disabled}
      style={{ pointerEvents: disabled ? 'none' : 'auto' }}
      whileHover={{ 
        scale: disabled ? 1 : 1.05, 
        boxShadow: disabled ? undefined : variant === 'default' ? '0 0 30px rgba(255, 61, 0, 0.6)' : 
                  variant === 'premium' ? '0 0 30px rgba(147, 51, 234, 0.6)' :
                  variant === 'cyber' ? '0 0 30px rgba(6, 182, 212, 0.6)' :
                  variant === 'neon' ? '0 0 30px rgba(34, 197, 94, 0.6)' :
                  '0 0 30px rgba(255, 61, 0, 0.4)'
      }}
      whileTap={{ scale: disabled ? 1 : 0.95 }}
      {...props}
    >
      <motion.div
        className="absolute inset-0 bg-gradient-to-r from-white/20 to-transparent"
        initial={{ x: '-100%' }}
        whileHover={{ x: disabled ? '-100%' : '100%' }}
        transition={{ duration: 0.6 }}
      />
      <span className="relative z-10 pointer-events-none">{children}</span>
    </motion.button>
  );
};

// Enhanced Glass Card
const GlassCard = ({ children, className = '', onClick, variant = 'default', ...props }: { children: React.ReactNode, className?: string, onClick?: () => void, variant?: string }) => {
  const [rotateX, setRotateX] = useState(0);
  const [rotateY, setRotateY] = useState(0);
  const [isHovered, setIsHovered] = useState(false);

  const handleMouseMove = (e: React.MouseEvent<HTMLDivElement>) => {
    const card = e.currentTarget;
    const rect = card.getBoundingClientRect();
    const centerX = rect.left + rect.width / 2;
    const centerY = rect.top + rect.height / 2;
    const rotateXValue = (e.clientY - centerY) / 10;
    const rotateYValue = (centerX - e.clientX) / 10;

    setRotateX(rotateXValue);
    setRotateY(rotateYValue);
    setIsHovered(true);
  };

  const handleMouseLeave = () => {
    setRotateX(0);
    setRotateY(0);
    setIsHovered(false);
  };

  const getCardStyle = () => {
    switch (variant) {
      case 'pricing':
        return 'bg-gradient-to-br from-white/15 to-white/5 border-white/25';
      case 'feature':
        return 'bg-gradient-to-br from-orange-500/15 to-red-500/5 border-orange-500/25';
      case 'deep':
        return 'bg-gradient-to-br from-purple-500/15 to-blue-500/5 border-purple-500/25';
      case 'cyber':
        return 'bg-gradient-to-br from-cyan-500/15 to-blue-500/5 border-cyan-500/25';
      default:
        return 'bg-white/10 border-white/20';
    }
  };

  return (
    <motion.div
      className={`p-6 rounded-3xl border backdrop-blur-md cursor-hover cursor-glass ${getCardStyle()} ${className}`}
      style={{
        backdropFilter: 'blur(25px)',
        transform: `perspective(1000px) rotateX(${rotateX}deg) rotateY(${rotateY}deg)`,
        transformStyle: 'preserve-3d',
      }}
      onMouseMove={handleMouseMove}
      onMouseLeave={handleMouseLeave}
      onClick={onClick}
      whileHover={{ scale: 1.02, y: -6 }}
      whileTap={{ scale: 0.98 }}
      animate={{
        boxShadow: isHovered 
          ? '0 30px 60px -12px rgba(0, 0, 0, 0.6), 0 0 40px rgba(255, 61, 0, 0.15)'
          : '0 15px 35px -5px rgba(0, 0, 0, 0.4)',
      }}
      transition={{ type: 'spring', stiffness: 300, damping: 30 }}
      {...props}
    >
      <div style={{ transform: 'translateZ(30px)' }}>
        {children}
      </div>
    </motion.div>
  );
};

// Text Shimmer Component
const TextShimmer = ({ children, className = '' }: { children: React.ReactNode, className?: string }) => (
  <motion.div
    className={`bg-gradient-to-r from-transparent via-white/60 to-transparent bg-clip-text text-transparent bg-[length:200%_100%] ${className}`}
    animate={{
      backgroundPosition: ['-200% 0%', '200% 0%']
    }}
    transition={{
      duration: 2,
      repeat: Infinity,
      ease: 'linear'
    }}
  >
    {children}
  </motion.div>
);

// Vapour Text Effect Component
const VapourText = ({ children, className = '' }: { children: React.ReactNode, className?: string }) => (
  <motion.div
    className={`relative inline-block ${className}`}
    whileHover="hover"
  >
    <motion.span
      className="relative z-10"
      variants={{
        hover: {
          textShadow: [
            '0 0 4px rgba(255,255,255,0.8)',
            '0 0 8px rgba(255,255,255,0.8)',
            '0 0 12px rgba(255,255,255,0.8)',
            '0 0 8px rgba(255,255,255,0.8)',
            '0 0 4px rgba(255,255,255,0.8)'
          ]
        }
      }}
      transition={{ duration: 0.5, repeat: Infinity, repeatType: 'reverse', ease: 'easeInOut' }}
    >
      {children}
    </motion.span>
    <motion.div
      className="absolute inset-0 bg-gradient-to-r from-orange-500/20 to-red-500/20 blur-sm"
      variants={{
        hover: {
          scale: [1, 1.1, 1],
          opacity: [0.3, 0.6, 0.3]
        }
      }}
      transition={{ duration: 0.5, repeat: Infinity, repeatType: 'reverse', ease: 'easeInOut' }}
    />
  </motion.div>
);

// Gender Types
const Gender = {
  MALE: 'male',
  FEMALE: 'female'
};

// FlameIcon Component
const FlameIcon = ({ className = '' }: { className?: string }) => (
  <motion.img
    src="/assets/fire-logo.png"
    alt="PapoFire Logo"
    className={`w-8 h-8 object-contain ${className}`}
    animate={{ 
      filter: [
        'drop-shadow(0 0 8px rgba(255,61,0,0.4))',
        'drop-shadow(0 0 15px rgba(255,87,34,0.7))',
        'drop-shadow(0 0 8px rgba(255,61,0,0.4))'
      ]
    }}
    transition={{ 
      duration: 2.5, 
      repeat: Infinity, 
      ease: 'easeInOut' 
    }}
  />
);

// Simplified Fire Particles
const FireParticles = () => {
  const [particles, setParticles] = useState<{ id: number; x: number; y: number; size: number; opacity: number; }[]>([]);

  useEffect(() => {
    try {
      const newParticles = Array.from({ length: 20 }, (_, i) => ({
        id: i,
        x: Math.random() * 800,
        y: Math.random() * 600,
        size: Math.random() * 4 + 2,
        opacity: Math.random() * 0.3 + 0.1,
      }));
      setParticles(newParticles);
    } catch (error) {
      console.error('Error creating particles:', error);
      setParticles([]);
    }
  }, []);

  if (particles.length === 0) {
    return null;
  }

  return (
    <div className="fixed inset-0 pointer-events-none z-0 overflow-hidden opacity-20">
      {particles.map((particle) => (
        <motion.div
          key={particle.id}
          className="absolute rounded-full bg-orange-500"
          style={{
            width: particle.size,
            height: particle.size,
            left: particle.x,
            top: particle.y,
            opacity: particle.opacity,
          }}
          animate={{
            y: [particle.y, particle.y - 200],
            x: [particle.x, particle.x + 30],
            opacity: [particle.opacity, 0],
            scale: [1, 1.2, 0.5],
          }}
          transition={{
            duration: 8,
            repeat: Infinity,
            ease: 'linear',
          }}
        />
      ))}
    </div>
  );
};

// PapoFire 3.0 - SPA único com Login + Planos + Gênero Fix COMPLETO
const PapoFireApp = () => {
  const { user } = useAuth() || {};
  const [step, setStep] = useState('landing'); // landing → home (auth integrado)
  const [gender, setGender] = useState('male');
  const [selectedPlatform, setSelectedPlatform] = useState('whatsapp');
  const [selectedTom, setSelectedTom] = useState('casual');
  const [selectedTopic, setSelectedTopic] = useState('');
  const [generatedIdeas, setGeneratedIdeas] = useState<string[]>([]);
  const [loading, setLoading] = useState(false);
  const [copiedIndex, setCopiedIndex] = useState<number | null>(null);
  const [showPlans, setShowPlans] = useState(false);
  const [showAuthModal, setShowAuthModal] = useState(false);
  const [isLoggedIn, setIsLoggedIn] = useState(false);
  const [userName, setUserName] = useState('');

  // LOGIN / REGISTER (sem reload) - MODAL INTEGRADO
  const [isLogin, setIsLogin] = useState(true);
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [authError, setAuthError] = useState('');

  // PapoFire 3.0 Enhanced States
  const [streak, setStreak] = useState(3);
  const [gems, setGems] = useState(24);
  const [vault, setVault] = useState<{ phrase: string, tags: string[], id: number }[]>([]);
  const [isListening, setIsListening] = useState(false);
  const [duels, setDuels] = useState<{ text: string, votes: number }[]>([]);
  const [dailyLootBox, setDailyLootBox] = useState<{ type: string, gems: number, phrases: number, trend: string } | null>(null);
  const [currentTheme, setCurrentTheme] = useState('dark');
  const [uploadProgress, setUploadProgress] = useState(0);
  const [showUploadSuccess, setShowUploadSuccess] = useState(false);
  
  // Upload states for Stories and Chat analysis
  const [uploadedImage, setUploadedImage] = useState<{ file: File, preview: string, name: string } | null>(null);
  const [uploadedChat, setUploadedChat] = useState<{ file: File, preview: string, name: string } | null>(null);
  const [isAnalyzingStory, setIsAnalyzingStory] = useState(false);
  const [isAnalyzingChat, setIsAnalyzingChat] = useState(false);

  // Refs to prevent input issues
  const emailRef = useRef<HTMLInputElement>(null);
  const passwordRef = useRef<HTMLInputElement>(null);
  const confirmPasswordRef = useRef<HTMLInputElement>(null);
  const topicRef = useRef<HTMLTextAreaElement>(null);
  
  // Prevent unnecessary re-renders
  const inputConfig = useMemo(() => ({
    autoComplete: 'off',
    spellCheck: false,
  }), []);

  // Check if user is logged in on mount
  useEffect(() => {
    if (user) {
      setStep('home');
    } else {
      setStep('landing');
    }
  }, [user]);

  const handleAuth = useCallback(() => {
    try {
      setAuthError('');
      
      if (!email.trim() || !password.trim()) {
        setAuthError('Preencha todos os campos');
        return;
      }
      
      if (!isLogin && password !== confirmPassword) {
        setAuthError('Senhas não coincidem');
        return;
      }
      
      if (!isLogin && password.length < 6) {
        setAuthError('Senha deve ter pelo menos 6 caracteres');
        return;
      }
      
      // Mock auth - salva no localStorage
      if (typeof window !== 'undefined' && window.localStorage) {
        localStorage.setItem('papofire-user', email.trim());
        localStorage.setItem('papofire-gender', gender);
      }
      setUserName(email.trim());
      setIsLoggedIn(true);
      setShowAuthModal(false);
      setStep('home');
      
      console.log('✅ Login realizado com sucesso!', { email: email.trim(), gender, step: 'home' });
    } catch (error) {
      console.error('Error in auth:', error);
      setAuthError('Erro interno. Tente novamente.');
    }
  }, [email, password, confirmPassword, isLogin, gender]);

  const handleLogout = useCallback(() => {
    try {
      if (typeof window !== 'undefined' && window.localStorage) {
        localStorage.removeItem('papofire-user');
        localStorage.removeItem('papofire-gender');
      }
      setIsLoggedIn(false);
      setStep('landing');
      setUserName('');
      setEmail('');
      setPassword('');
      setConfirmPassword('');
      setAuthError('');
      setShowAuthModal(false);
      setGeneratedIdeas([]);
      setSelectedTopic('');
    } catch (error) {
      console.error('Error in logout:', error);
    }
  }, []);

  // PLANOS INLINE
  const plans = [
    { 
      id: 'pro', 
      name: 'PapoFire PRO', 
      price: 'R$ 9,90', 
      link: 'https://pay.cakto.com.br/35inig4_428595', 
      icon: <Rocket className="w-6 h-6" />,
      features: ['Ideias ilimitadas + micro-romance', 'TrendMiner', 'Sem anúncios', 'Suporte prioritário'],
      color: 'from-orange-500 to-red-500'
    },
    { 
      id: 'ultra', 
      name: 'PapoFire ULTRA', 
      price: 'R$ 19,90', 
      link: 'https://pay.cakto.com.br/3nqtcdf', 
      icon: <Crown className="w-6 h-6" />,
      features: ['Tudo do PRO', 'Análise psicológica', 'IA personalizada', 'Coaching avançado'],
      color: 'from-purple-500 to-pink-500'
    },
  ];

  // AI PROMPT MULTI-PLATAFORMA ULTRA-ESPECÍFICO
  const buildPrompt = (input: string) => {
    const platformConfig = {
      whatsapp: { maxChars: 160, style: 'direto e casual', emojis: '🔥😏❤️😂🤙' },
      instagram: { maxChars: 100, style: 'visual e atrativo', emojis: '😍✨💖🔥📸' },
      tiktok: { maxChars: 150, style: 'viral e enérgico', emojis: '🔥💯✨😎🚀' },
      twitter: { maxChars: 280, style: 'inteligente e conciso', emojis: '🔥💭✨🎯💡' }
    };
    
    const config = platformConfig[selectedPlatform as keyof typeof platformConfig];
    
    // GÊNERO CONTEXT SUPER ESPECÍFICO
    const genderContext = gender === 'male' 
      ? 'homem brasileiro tentando conquistar uma mulher' 
      : 'mulher brasileira tentando conquistar um homem';
    
    const tomInstructions = {
      casual: 'Seja descontraído e natural',
      flerte: 'Use charme sutil e confiança',
      fofo: 'Seja carinhoso e doce',
      confiante: 'Mostre personalidade forte'
    };
    
    // INSTRUÇÕES ULTRA ESPECÍFICAS POR GÊNERO
    const genderInstructions = gender === 'male' 
      ? `PAPEL: Você é um HOMEM BRASILEIRO falando para uma MULHER no ${selectedPlatform.toUpperCase()}.
LINGUAGEM OBRIGATÓRIA:
- Use: "mano", "cara", "brother", "véi", "parceiro", "massa", "da hora", "sinistro"
- Tom: direto, confiante, ${tomInstructions[selectedTom as keyof typeof tomInstructions]}
- Exemplos: "Mano, que da hora!", "Cara, adorei!", "Véi, conta aí", "Eita, massa!"
- NUNCA use: "amor", "linda", "gata", "querida" (isso é linguagem feminina)`
      : `PAPEL: Você é uma MULHER BRASILEIRA falando para um HOMEM no ${selectedPlatform.toUpperCase()}.
LINGUAGEM OBRIGATÓRIA:
- Use: "amor", "gato", "querido", "fofo", "gatinho", "benzinho", "lindinho"
- Tom: carinhosa, envolvente, ${tomInstructions[selectedTom as keyof typeof tomInstructions]}
- Exemplos: "Amor, que lindo!", "Gato, adorei!", "Querido, conta pra mim!", "Fofo, que massa!"
- NUNCA use: "mano", "cara", "brother" (isso é linguagem masculina)`;

    return `🚀 PAPOFIRE 3.0 MULTI-PLATFORM: ${genderContext}

=== PLATAFORMA: ${selectedPlatform.toUpperCase()} ===
${genderInstructions}

CONFIGURAÇÃO ESPECÍFICA:
- Plataforma: ${selectedPlatform} (${config.style})
- Máximo: ${config.maxChars} caracteres
- Tom: ${selectedTom} (${tomInstructions[selectedTom as keyof typeof tomInstructions]})
- Tópico: "${input}"
- Emojis permitidos: ${config.emojis}

REGRAS POR PLATAFORMA:
${selectedPlatform === 'whatsapp' ? '• Mensagens diretas e rápidas\n• Pode usar áudio/chamada\n• Informal total' : ''}
${selectedPlatform === 'instagram' ? '• Referência visual se possível\n• Mais estético\n• CTAs para Stories/DM' : ''}
${selectedPlatform === 'tiktok' ? '• Use trends atuais\n• Seja viral e enérgico\n• Emojis chamativos' : ''}
${selectedPlatform === 'twitter' ? '• Pode usar threads\n• Hashtags relevantes\n• Conciso mas impactante' : ''}

REGRAS CRÍTICAS:
• Gírias BR naturais por gênero
• Emojis estratégicos específicos da plataforma
• NUNCA passar de ${config.maxChars} caracteres
• RESPEITE o gênero selecionado COMPLETAMENTE
• Seja NATURAL para ${gender === 'male' ? 'homem brasileiro' : 'mulher brasileira'}
• Adapte ao estilo da plataforma: ${config.style}

Gere EXATAMENTE 5 opções separadas por "---".
Cada opção deve ser PERFEITA para ${selectedPlatform.toUpperCase()} e soar NATURAL para ${gender === 'male' ? 'um homem' : 'uma mulher'} brasileiro(a)!`;
  };

  // Enhanced generate with real Gemini API
  const generateConversationIdeas = useCallback(async () => {
    if (!selectedTopic.trim()) return;
    
    setLoading(true);
    
    try {
      const prompt = buildPrompt(selectedTopic);
      const GEMINI_API_KEY = 'AIzaSyDYhIQ5ozQGM5tQxMm3Az1IkeOjp-brgPA';

      const response = await fetch(`https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent?key=${GEMINI_API_KEY}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          contents: [{
            parts: [{
              text: prompt
            }]
          }]
        })
      });

      if (!response.ok) {
        throw new Error(`Gemini API error: ${response.status}`);
      }

      const data = await response.json();
      const responseText = data.candidates[0].content.parts[0].text;
      
      // Parse responses separated by ---
      const ideas = responseText.split('---').map((idea: string) => idea.trim()).filter((idea: string) => idea.length > 0);
      setGeneratedIdeas(ideas.slice(0, 5));
      
      // Add streak and gems
      setStreak(prev => Math.min(prev + 1, 7));
      setGems(prev => prev + 2);
      
      // Check for daily loot box
      if (streak >= 6 && !dailyLootBox) {
        setDailyLootBox({
          type: 'daily',
          gems: 10,
          phrases: 3,
          trend: 'random'
        });
      }
      
    } catch (error) {
      console.error('Erro na API do Gemini:', error);
      // Enhanced fallback with MULTI-PLATFORM GENDER SYSTEM
      const platformMockIdeas = {
        whatsapp: gender === 'male' ? [
          `Mano, ${selectedTopic.toLowerCase()} é vida! Qual foi a experiência mais marcante? 🔥`,
          `Cara, imagina a gente curtindo ${selectedTopic.toLowerCase()} juntos... seria massa! 😏`,
          `Véi, que da hora ${selectedTopic.toLowerCase()}! Como você descobriu essa paixão? 🤙`,
          `Brother, ${selectedTopic.toLowerCase()} é sinistro! Conta aí sua história pra mim`,
          `Eita, depois dessa sobre ${selectedTopic.toLowerCase()}, bora marcar um rolê? 😂`
        ] : [
          `Amor, ${selectedTopic.toLowerCase()} é tudo de bom! Qual foi a experiência mais marcante? 🔥`,
          `Gato, imagina a gente curtindo ${selectedTopic.toLowerCase()} juntinhos... seria perfeito! 😏`,
          `Querido, que da hora ${selectedTopic.toLowerCase()}! Como você descobriu essa paixão? 🤙`,
          `Fofo, ${selectedTopic.toLowerCase()} é massa demais! Conta essa história pra mim`,
          `Lindinho, depois dessa sobre ${selectedTopic.toLowerCase()}, vamos marcar um encontro? ❤️`
        ],
        instagram: gender === 'male' ? [
          `Cara, esse ${selectedTopic.toLowerCase()} no seu story ficou sinistro! 📸🔥`,
          `Mano, adorei esse ${selectedTopic.toLowerCase()}! Onde foi isso? 😍`,
          `Véi, que foto massa! Conta mais sobre ${selectedTopic.toLowerCase()} ✨`,
          `Brother, ${selectedTopic.toLowerCase()} é o seu forte mesmo! 💯`,
          `Eita, reagindo ao story: ${selectedTopic.toLowerCase()} perfeito! 🤙`
        ] : [
          `Amor, que ${selectedTopic.toLowerCase()} lindo no seu story! 😍✨`,
          `Gato, adorei esse ${selectedTopic.toLowerCase()}! Me conta mais 💖`,
          `Querido, que foto perfeita! ${selectedTopic.toLowerCase()} é sua paixão? 📸`,
          `Fofo, esse ${selectedTopic.toLowerCase()} ficou um espetáculo! ✨`,
          `Lindinho, reagindo ao story: que ${selectedTopic.toLowerCase()} incrível! 💕`
        ],
        tiktok: gender === 'male' ? [
          `Cara, esse ${selectedTopic.toLowerCase()} tá viral! 🔥💯`,
          `Mano, você manda bem em ${selectedTopic.toLowerCase()}! ✨`,
          `Véi, que vídeo massa sobre ${selectedTopic.toLowerCase()}! 🚀`,
          `Brother, ${selectedTopic.toLowerCase()} é seu forte! 😎`,
          `Eita, seguindo para mais ${selectedTopic.toLowerCase()}! 🤙`
        ] : [
          `Amor, que ${selectedTopic.toLowerCase()} incrível! 😍🔥`,
          `Gato, você arrasa em ${selectedTopic.toLowerCase()}! ✨💖`,
          `Querido, seguindo para mais ${selectedTopic.toLowerCase()}! 🚀`,
          `Fofo, que talento para ${selectedTopic.toLowerCase()}! 💯`,
          `Lindinho, esse ${selectedTopic.toLowerCase()} ficou perfeito! ✨`
        ],
        twitter: gender === 'male' ? [
          `Cara, essa sobre ${selectedTopic.toLowerCase()} é genial! 🔥 Pensou em expandir?`,
          `Mano, ${selectedTopic.toLowerCase()} é mesmo fascinante. Qual sua próxima? 🧠`,
          `Véi, concordo total sobre ${selectedTopic.toLowerCase()}! Realtweet merecido 🤙`,
          `Brother, essa visão sobre ${selectedTopic.toLowerCase()} tá on point! 💡`,
          `Eita, thread sobre ${selectedTopic.toLowerCase()} quando? Seguindo! 🎯`
        ] : [
          `Amor, que reflexão linda sobre ${selectedTopic.toLowerCase()}! 💭✨`,
          `Gato, adorei seu ponto sobre ${selectedTopic.toLowerCase()}! Concordo demais 💖`,
          `Querido, essa sobre ${selectedTopic.toLowerCase()} merece RT! Seguindo 🔥`,
          `Fofo, que inteligência para falar de ${selectedTopic.toLowerCase()}! 🧠`,
          `Lindinho, thread sobre ${selectedTopic.toLowerCase()} quando? Ansiosa! ✨`
        ]
      };
      
      const mockIdeas = platformMockIdeas[selectedPlatform as keyof typeof platformMockIdeas] || platformMockIdeas.whatsapp;
      
      setGeneratedIdeas(mockIdeas);
    } finally {
      setLoading(false);
    }
  }, [selectedTopic, gender, selectedPlatform, selectedTom, streak, dailyLootBox]);

  const copyToClipboard = useCallback(async (text: string, index: number) => {
    try {
      await navigator.clipboard.writeText(text);
      setCopiedIndex(index);
      if (navigator.vibrate) {
        navigator.vibrate([50, 50, 50]);
      }
      setTimeout(() => setCopiedIndex(null), 2500);
    } catch (err) {
      console.error('Failed to copy text:', err);
      // Fallback for older browsers
      try {
        const textArea = document.createElement('textarea');
        textArea.value = text;
        textArea.style.position = 'fixed';
        textArea.style.left = '-9999px';
        textArea.style.top = '-9999px';
        textArea.style.opacity = '0';
        document.body.appendChild(textArea);
        textArea.focus();
        textArea.select();
        document.execCommand('copy');
        document.body.removeChild(textArea);
        setCopiedIndex(index);
        setTimeout(() => setCopiedIndex(null), 2500);
      } catch (fallbackErr) {
        console.error('Fallback copy failed:', fallbackErr);
        setCopiedIndex(index);
        setTimeout(() => setCopiedIndex(null), 2500);
      }
    }
  }, []);

  // PapoFire 3.0 Enhanced Functions
  const handleRoletaResult = (tema: { label: string }) => {
    setSelectedTopic(tema.label);
    generateConversationIdeas();
  };

  const handleLongPress = (phrase: string) => {
    const tags = extractTagsFromPhrase(phrase);
    setVault(prev => [...prev, { phrase, tags, id: Date.now() }]);
    setGems(prev => prev + 1);
  };

  const extractTagsFromPhrase = (phrase: string) => {
    const words = phrase.toLowerCase().split(' ');
    const commonTags = ['viagem', 'comida', 'música', 'filme', 'série', 'esporte', 'festa', 'trabalho'];
    return words.filter(word => commonTags.includes(word) || word.length > 6).slice(0, 3);
  };

  const startVoiceInput = () => {
    if ('webkitSpeechRecognition' in window) {
      const recognition = new (window as any).webkitSpeechRecognition();
      recognition.lang = 'pt-BR';
      recognition.continuous = false;
      recognition.interimResults = false;

      recognition.onstart = () => {
        setIsListening(true);
      };

      recognition.onresult = (event: any) => {
        const transcript = event.results[0][0].transcript;
        setSelectedTopic(transcript);
        generateConversationIdeas();
      };

      recognition.onend = () => {
        setIsListening(false);
      };

      recognition.start();
    } else {
      alert('Navegador não suporta reconhecimento de voz');
    }
  };

  const handleDuelVote = (index: number) => {
    setDuels(prev => prev.map((duel, i) =>
      i === index ? { ...duel, votes: (duel.votes || 0) + 1 } : duel
    ));
    setGems(prev => prev + 1);
  };

  // Upload handlers for Stories and Chat analysis
  const handleImageUpload = useCallback((file: File) => {
    if (!file) return;

    // Validate file type
    const validTypes = ['image/jpeg', 'image/png', 'image/webp'];
    if (!validTypes.includes(file.type)) {
      alert('Formato não suportado! Use JPG, PNG ou WEBP.');
      return;
    }

    // Validate file size (10MB max)
    if (file.size > 10 * 1024 * 1024) {
      alert('Arquivo muito grande! Máximo 10MB.');
      return;
    }

    // Simulate upload progress
    setUploadProgress(0);
    const uploadInterval = setInterval(() => {
      setUploadProgress(prev => {
        if (prev >= 100) {
          clearInterval(uploadInterval);
          setShowUploadSuccess(true);
          return 100;
        }
        return prev + 20;
      });
    }, 200);

    // Create image preview
    const reader = new FileReader();
    reader.onload = (e) => {
      setTimeout(() => {
        setUploadedImage({
          file: file,
          preview: e.target?.result as string,
          name: file.name
        });
        setUploadProgress(0);
      }, 1000);
    };
    reader.readAsDataURL(file);
  }, []);

  const handleChatUpload = useCallback((file: File) => {
    if (!file) return;

    // Validate file type
    const validTypes = ['image/jpeg', 'image/png', 'image/webp'];
    if (!validTypes.includes(file.type)) {
      alert('Formato não suportado! Use JPG, PNG ou WEBP.');
      return;
    }

    // Validate file size (10MB max)
    if (file.size > 10 * 1024 * 1024) {
      alert('Arquivo muito grande! Máximo 10MB.');
      return;
    }

    // Simulate upload progress
    setUploadProgress(0);
    const uploadInterval = setInterval(() => {
      setUploadProgress(prev => {
        if (prev >= 100) {
          clearInterval(uploadInterval);
          setShowUploadSuccess(true);
          return 100;
        }
        return prev + 25;
      });
    }, 150);

    // Create chat preview
    const reader = new FileReader();
    reader.onload = (e) => {
      setTimeout(() => {
        setUploadedChat({
          file: file,
          preview: e.target?.result as string,
          name: file.name
        });
        setUploadProgress(0);
      }, 800);
    };
    reader.readAsDataURL(file);
  }, []);

  const analyzeStory = useCallback(async () => {
    if (!uploadedImage) return;

    setIsAnalyzingStory(true);
    
    try {
      // Simulate analysis based on image type
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // Generate ideas based on analyzed story
      const storyTopics = [
        'academia', 'fitness', 'comida', 'paisagem', 'selfie', 
        'festa', 'viagem', 'pet', 'trabalho', 'música'
      ];
      
      const randomTopic = storyTopics[Math.floor(Math.random() * storyTopics.length)];
      setSelectedTopic(`story_${randomTopic}`);
      
      // Generate story-specific responses
      await generateConversationIdeas();
      
    } catch (error) {
      console.error('Erro na análise:', error);
      alert('Erro ao analisar a imagem. Tente novamente.');
    } finally {
      setIsAnalyzingStory(false);
    }
  }, [uploadedImage, generateConversationIdeas]);

  const analyzeChat = useCallback(async () => {
    if (!uploadedChat) return;

    setIsAnalyzingChat(true);
    
    try {
      // Simulate chat analysis
      await new Promise(resolve => setTimeout(resolve, 3000));
      
      // Generate ideas based on analyzed chat
      const chatContexts = [
        'paquera_inicial', 'conversa_quente', 'relacionamento', 
        'conversa_travada', 'flerte_avancado', 'primeira_conversa'
      ];
      
      const randomContext = chatContexts[Math.floor(Math.random() * chatContexts.length)];
      
      // Generate chat-specific responses
      await generateConversationIdeas();
      
    } catch (error) {
      console.error('Erro na análise:', error);
      alert('Erro ao analisar a conversa. Tente novamente.');
    } finally {
      setIsAnalyzingChat(false);
    }
  }, [uploadedChat, generateConversationIdeas]);

  // PÁGINA DE ANÁLISE DE STORIES
  const renderStories = () => {
    const fileInputRef = useRef<HTMLInputElement>(null);

    const handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
      const file = event.target.files?.[0];
      if (!file) return;

      // Verificar tipo do arquivo
      const validTypes = ['image/jpeg', 'image/png', 'image/webp'];
      if (!validTypes.includes(file.type)) {
        alert('Formato não suportado! Use JPG, PNG ou WEBP.');
        return;
      }

      // Verificar tamanho (10MB max)
      if (file.size > 10 * 1024 * 1024) {
        alert('Arquivo muito grande! Máximo 10MB.');
        return;
      }

      // Simular progresso de upload
      setUploadProgress(0);
      const uploadInterval = setInterval(() => {
        setUploadProgress(prev => {
          if (prev >= 100) {
            clearInterval(uploadInterval);
            setShowUploadSuccess(true);
            return 100;
          }
          return prev + 20;
        });
      }, 200);

      // Criar preview da imagem
      const reader = new FileReader();
      reader.onload = (e) => {
        setTimeout(() => {
          setUploadedImage({
            file: file,
            preview: e.target?.result as string,
            name: file.name
          });
          setUploadProgress(0);
        }, 1000);
      };
      reader.readAsDataURL(file);
    };

    const analyzeStory = async () => {
      if (!uploadedImage) return;

      setIsAnalyzingStory(true);
      
      try {
        // Simular análise com base no tipo de imagem
        await new Promise(resolve => setTimeout(resolve, 2000));
        
        // Gerar ideias baseadas no story "analisado"
        const storyTopics = [
          'academia', 'fitness', 'comida', 'paisagem', 'selfie',
          'festa', 'viagem', 'pet', 'trabalho', 'música'
        ];
        
        const randomTopic = storyTopics[Math.floor(Math.random() * storyTopics.length)];
        setSelectedTopic(`story_${randomTopic}`);
        
        // Gerar respostas específicas para stories
        await generateConversationIdeas();
        
      } catch (error) {
        console.error('Erro na análise:', error);
        alert('Erro ao analisar a imagem. Tente novamente.');
      } finally {
        setIsAnalyzingStory(false);
      }
    };

    const removeImage = () => {
      setUploadedImage(null);
      setGeneratedIdeas([]);
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    };

    return (
      <motion.div
        className="min-h-screen p-4 bg-gradient-to-br from-purple-900 via-black to-blue-900 text-white relative overflow-hidden"
        initial={{ x: 300, opacity: 0 }}
        animate={{ x: 0, opacity: 1 }}
        exit={{ x: -300, opacity: 0 }}
      >
        <FireParticles />
        <SquaresBackground />
        
        <div className="relative z-10 max-w-md mx-auto">
          {/* Header */}
          <div className="flex items-center mb-8 pt-8">
            <motion.button
              onClick={() => setStep('home')}
              className="mr-4 p-3 rounded-full hover:bg-gray-800/50 transition-all cursor-hover"
              whileHover={{ scale: 1.1, rotate: -10 }}
              whileTap={{ scale: 0.9 }}
            >
              <ChevronLeft className="w-6 h-6" />
            </motion.button>
            <div>
              <h1 className="text-2xl font-bold">Análise de Stories 📸</h1>
              <p className="text-sm text-gray-400">Resposta ideal para qualquer story 🔥</p>
            </div>
          </div>

          <div className="space-y-6">
            {/* Upload Area */}
            <GlassCard className="text-center">
              <div className="p-8">
                {!uploadedImage ? (
                  <>
                    <motion.div
                      className="w-24 h-24 mx-auto mb-6 bg-purple-500/20 rounded-2xl flex items-center justify-center cursor-pointer"
                      whileHover={{ scale: 1.1 }}
                      whileTap={{ scale: 0.9 }}
                      onClick={() => fileInputRef.current?.click()}
                    >
                      <Upload className="w-12 h-12 text-purple-400" />
                    </motion.div>
                    
                    <h3 className="text-xl font-bold mb-4">Envie o Print do Story</h3>
                    <p className="text-gray-400 mb-6">
                      Faça print do story que você quer responder e nossa IA vai analisar o contexto
                    </p>
                    
                    <div className="space-y-4">
                      <input
                        ref={fileInputRef}
                        type="file"
                        accept="image/jpeg,image/png,image/webp"
                        onChange={handleImageUpload}
                        className="hidden"
                      />
                      
                      <GlowingButton
                        variant="premium"
                        className="w-full py-3"
                        onClick={() => fileInputRef.current?.click()}
                      >
                        <Camera className="w-5 h-5 mr-2" />
                        Selecionar Imagem
                      </GlowingButton>
                      
                      <p className="text-xs text-gray-500">
                        Formatos aceitos: JPG, PNG, WEBP • Máx: 10MB
                      </p>
                    </div>
                  </>
                ) : (
                  <>
                    {/* Preview da Imagem */}
                    <div className="mb-6">
                      <div className="relative inline-block">
                        <img
                          src={uploadedImage.preview}
                          alt="Story preview"
                          className="max-w-full max-h-64 rounded-xl border border-purple-500/30"
                        />
                        <button
                          onClick={removeImage}
                          className="absolute -top-2 -right-2 w-8 h-8 bg-red-500 rounded-full flex items-center justify-center hover:bg-red-600 transition-colors"
                        >
                          <X className="w-4 h-4 text-white" />
                        </button>
                      </div>
                    </div>
                    
                    <h3 className="text-xl font-bold mb-4">
                      {isAnalyzingStory ? 'Analisando Story...' : 'Story Carregado!'}
                    </h3>
                    <p className="text-gray-400 mb-6">
                      {isAnalyzingStory ? 'Nossa IA está analisando o contexto...' : 'Clique para analisar e gerar respostas'}
                    </p>
                    
                    <GlowingButton
                      variant="premium"
                      className="w-full py-3"
                      onClick={analyzeStory}
                      disabled={isAnalyzingStory}
                    >
                      {isAnalyzingStory ? (
                        <>
                          <Loader2 className="w-5 h-5 mr-2 animate-spin" />
                          Analisando...
                        </>
                      ) : (
                        <>
                          <Brain className="w-5 h-5 mr-2" />
                          Analisar Story
                        </>
                      )}
                    </GlowingButton>
                  </>
                )}
              </div>
            </GlassCard>

          {/* Exemplos de Stories */}
          <div className="space-y-4">
            <h4 className="font-bold text-center">📱 Exemplos de Stories</h4>
            <div className="grid grid-cols-2 gap-3">
              {[
                { emoji: '🏋️', text: 'Academia', topic: 'fitness' },
                { emoji: '🍕', text: 'Comida', topic: 'comida' },
                { emoji: '🌅', text: 'Paisagem', topic: 'viagem' },
                { emoji: '🎵', text: 'Música', topic: 'música' }
              ].map((example, index) => (
                <motion.button
                  key={index}
                  onClick={() => {
                    setSelectedTopic(example.topic);
                    generateConversationIdeas();
                  }}
                  className="p-4 bg-white/5 rounded-xl border border-white/10 hover:border-purple-500/30 transition-all"
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                >
                  <div className="text-2xl mb-2">{example.emoji}</div>
                  <div className="text-sm font-medium">{example.text}</div>
                </motion.button>
              ))}
            </div>
          </div>



          {/* Respostas Geradas */}
          <AnimatePresence>
            {generatedIdeas.length > 0 && (
              <motion.div
                className="space-y-4"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
              >
                <h3 className="text-lg font-bold text-center flex items-center justify-center gap-2">
                  🔥 Respostas para o Story
                  <span className="ml-2 px-2 py-1 bg-purple-500/20 text-purple-400 rounded text-xs font-bold">
                    IA VISUAL
                  </span>
                </h3>
                
                {generatedIdeas.map((idea, index) => (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: index * 0.1 }}
                    className="p-4 bg-purple-500/10 rounded-xl border border-purple-500/20 hover:border-purple-500/40 transition-all cursor-pointer"
                    onClick={() => copyToClipboard(idea, index)}
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                  >
                    <div className="flex items-start justify-between">
                      <p className="text-sm flex-1 leading-relaxed pr-3">
                        {idea}
                      </p>
                      <motion.div
                        animate={copiedIndex === index ? { scale: [1, 1.3, 1] } : {}}
                      >
                        {copiedIndex === index ? (
                          <Check className="w-5 h-5 text-green-500" />
                        ) : (
                          <Copy className="w-5 h-5 text-gray-400" />
                        )}
                      </motion.div>
                    </div>
                  </motion.div>
                ))}
              </motion.div>
            )}
          </AnimatePresence>
        </div>
      </div>
    </motion.div>
  );
  };

  return (
    <div className="min-h-screen bg-black text-white font-sans relative overflow-hidden">
      {/* Auth Modal - sempre disponível */}
      <AuthModal
        show={showAuthModal}
        onClose={() => setShowAuthModal(false)}
        isLogin={isLogin}
        setIsLogin={setIsLogin}
        email={email}
        setEmail={setEmail}
        password={password}
        setPassword={setPassword}
        confirmPassword={confirmPassword}
        setConfirmPassword={setConfirmPassword}
        showPassword={showPassword}
        setShowPassword={setShowPassword}
        gender={gender}
        setGender={setGender}
        authError={authError}
        handleAuth={handleAuth}
        inputConfig={inputConfig}
        emailRef={emailRef}
        passwordRef={passwordRef}
        confirmPasswordRef={confirmPasswordRef}
      />
      
      {/* Upload Progress */}
      <UploadProgress progress={uploadProgress} isVisible={uploadProgress > 0} />
      
      {/* Upload Success */}
      <UploadSuccess
        isVisible={showUploadSuccess}
        onClose={() => setShowUploadSuccess(false)}
      />
      
      <AnimatePresence mode="wait">
        {step === 'landing' && (
          <motion.div key="landing">
            <LandingPage 
              onGetStarted={() => setShowAuthModal(true)}
              onLoginClick={() => setShowAuthModal(true)}
            />
          </motion.div>
        )}
        {step === 'home' && (
          <motion.div key="home">
            <HomePage onNavigate={setStep} />
          </motion.div>
        )}
        {step === 'topics' && (
          <motion.div key="topics">
            {renderTopics()}
          </motion.div>
        )}
        {step === 'stories' && (
          <motion.div key="stories">
            {renderStories()}
          </motion.div>
        )}
        {step === 'chat' && (
          <motion.div key="chat">
            <ChatPage onBack={() => setStep('home')} />
          </motion.div>
        )}
        {step === 'roleta' && (
          <motion.div key="roleta">
            {renderRoleta()}
          </motion.div>
        )}
      </AnimatePresence>
      

    </div>
  );
};

function AppContent() {
  const { user } = useAuth() || {};
  const [showAuthModal, setShowAuthModal] = useState(false);
  const [isLogin, setIsLogin] = useState(true);
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [gender, setGender] = useState('male');
  const [authError, setAuthError] = useState('');
  const emailRef = useRef(null);
  const passwordRef = useRef(null);
  const confirmPasswordRef = useRef(null);
  const inputConfig = useMemo(() => ({ autoComplete: 'off', spellCheck: false }), []);

  // Função de autenticação real com Supabase
  const handleAuth = async () => {
    setAuthError('');
    if (!email.trim() || !password.trim()) {
      setAuthError('Preencha todos os campos');
      return;
    }
    if (!isLogin && password !== confirmPassword) {
      setAuthError('Senhas não coincidem');
      return;
    }
    if (!isLogin && password.length < 6) {
      setAuthError('Senha deve ter pelo menos 6 caracteres');
      return;
    }
    try {
      if (isLogin) {
        const { error } = await supabase.auth.signInWithPassword({ email, password });
        if (error) {
          setAuthError(error.message || 'Erro ao autenticar.');
          console.error('Erro Supabase login:', error);
        } else {
          setShowAuthModal(false);
        }
      } else {
        const { error } = await supabase.auth.signUp({ email, password });
        if (error) {
          setAuthError(error.message || 'Erro ao autenticar.');
          console.error('Erro Supabase cadastro:', error);
        } else {
          setShowAuthModal(false);
        }
      }
    } catch (err) {
      setAuthError(err?.message || 'Erro ao autenticar.');
      console.error('Erro inesperado na autenticação:', err);
    }
  };

  if (!user) {
    return (
      <>
        <LandingPage
          onGetStarted={() => { setIsLogin(false); setShowAuthModal(true); }}
          onLoginClick={() => { setIsLogin(true); setShowAuthModal(true); }}
        />
        <AuthModal
          show={showAuthModal}
          onClose={() => setShowAuthModal(false)}
          isLogin={isLogin}
          setIsLogin={setIsLogin}
          email={email}
          setEmail={setEmail}
          password={password}
          setPassword={setPassword}
          confirmPassword={confirmPassword}
          setConfirmPassword={setConfirmPassword}
          showPassword={showPassword}
          setShowPassword={setShowPassword}
          gender={gender}
          setGender={setGender}
          authError={authError}
          handleAuth={handleAuth}
          inputConfig={inputConfig}
          emailRef={emailRef}
          passwordRef={passwordRef}
          confirmPasswordRef={confirmPasswordRef}
        />
      </>
    );
  }
  return (
    <ProtectedRoute>
      <PapoFireApp />
    </ProtectedRoute>
  );
}

export default function AppWrapper() {
  return (
    <AuthProvider>
      <AppContent />
    </AuthProvider>
  );
}
