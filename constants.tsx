import React from 'react';
import type { ReactNode } from 'react';
import { SocialPlatformId, ToneId } from './types';
import { MessageCircle, Camera, Brain, Shuffle, <PERSON>, HandHeart, Bot, Heart, Sparkles, Zap, Crown } from 'lucide-react';
import { 
    FireIcon as PapoFireLogo,
    BrainCircuitIcon, 
    ConversationIcon, 
    IdeaIcon,
    ComplimentIcon,
    StoryAnalysisIcon,
    ChatAnalysisIcon,
    DateIdeaIcon,
    WhatsAppIcon,
    InstagramIcon,
    TikTokIcon,
    TwitterIcon
} from './components/ui/icons';

export const FlameIcon = PapoFireLogo;

export const SOCIAL_PLATFORMS = [
  { id: SocialPlatformId.WhatsApp, label: 'WhatsApp', color: 'from-green-500 to-green-600', chars: '≤160', icon: <WhatsAppIcon className="w-5 h-5" /> },
  { id: SocialPlatformId.Instagram, label: 'Instagram', color: 'from-pink-500 to-purple-600', chars: '≤100', icon: <InstagramIcon className="w-5 h-5" /> },
  { id: SocialPlatformId.TikTok, label: 'TikTok', color: 'from-black to-pink-500', chars: '≤150', icon: <TikTokIcon className="w-5 h-5" /> },
  { id: SocialPlatformId.Twitter, label: 'Twitter', color: 'from-blue-400 to-blue-600', chars: '≤280', icon: <TwitterIcon className="w-5 h-5" /> },
];

export const TONES = [
  { id: ToneId.Casual, label: 'Casual', color: 'from-blue-500 to-cyan-500', icon: <Smile className="w-4 h-4" /> },
  { id: ToneId.Flerte, label: 'Flerte', color: 'from-purple-500 to-pink-500', icon: <Sparkles className="w-4 h-4" /> },
  { id: ToneId.Fofo, label: 'Fofo', color: 'from-pink-500 to-rose-500', icon: <Heart className="w-4 h-4" /> },
  { id: ToneId.Confiante, label: 'Confiante', color: 'from-orange-500 to-red-500', icon: <Zap className="w-4 h-4" /> },
  { id: ToneId.RomanticoSutil, label: 'Romântico', color: 'from-pink-400 to-purple-400', icon: <Crown className="w-4 h-4 fill-current" /> },
];

export const FEATURE_CARDS: { title: string; description: string; page: string; color: string; features: string[]; icon: ReactNode }[] = [
    {
        title: 'Gerador de Elogios',
        description: 'Crie elogios sinceros e específicos.',
        page: 'generator_compliment',
        color: 'purple',
        features: ['Baseado em características', 'Tons românticos e fofos', 'Evite o "lindo(a)" genérico'],
        icon: <ComplimentIcon className="w-6 h-6" />
    },
    {
        title: 'Análise de Story',
        description: 'Responda a Stories com a mensagem perfeita para puxar assunto.',
        page: 'generator_story',
        color: 'orange',
        features: ['Ideal para Instagram & WhatsApp', 'Respostas criativas e contextuais', 'Puxa assunto com qualquer foto'],
        icon: <StoryAnalysisIcon className="w-6 h-6" />
    },
    {
        title: 'Análise de Conversa',
        description: 'Envie um print do chat e receba respostas para continuar o papo.',
        page: 'generator_chat',
        color: 'blue',
        features: ['Analisa o contexto do chat', 'Sugestões para a próxima mensagem', 'Funciona com WhatsApp, Insta, etc.'],
        icon: <ChatAnalysisIcon className="w-6 h-6" />
    },
    {
        title: 'Ideias para Encontros',
        description: 'Sugestões de encontros e momentos especiais.',
        page: 'generator_date_idea',
        color: 'green',
        features: ['Para todos os gostos', 'Do casual ao super romântico', 'Surpreenda seu par'],
        icon: <DateIdeaIcon className="w-6 h-6" />
    }
];

export const LANDING_FEATURES = [
    {
        title: 'IA por Gênero',
        description: 'Respostas customizadas para homem e mulher.',
        icon: <BrainCircuitIcon className="w-6 h-6 text-white"/>
    },
    {
        title: 'Gírias Brasileiras',
        description: 'Linguagem natural que realmente funciona.',
        icon: <ConversationIcon className="w-6 h-6 text-white"/>
    },
    {
        title: 'Whatsapp Ready',
        description: 'Copie e cole direto no seu chat.',
        icon: <IdeaIcon className="w-6 h-6 text-white"/>
    }
];

export const LANDING_STATS = [
    {
        value: '10K+',
        label: 'Conversas/mês'
    },
    {
        value: '95%',
        label: 'Taxa de Sucesso'
    },
    {
        value: '24/7',
        label: 'IA Disponível'
    }
];

export const ROLETAS_TEMAS = [
    { label: 'Viagem', color: '#3b82f6' },
    { label: 'Comida', color: '#ef4444' },
    { label: 'Música', color: '#8b5cf6' },
    { label: 'Meme', color: '#f59e0b' },
    { label: 'Séries', color: '#10b981' },
    { label: 'Esportes', color: '#f97316' },
    { label: 'Festa', color: '#06b6d4' },
    { label: 'Cantadas', color: '#ec4899' },
    { label: 'Romance', color: '#f472b6' },
    { label: 'Encontros', color: '#a78bfa' },
    { label: 'Declarações', color: '#e11d48' },
];
