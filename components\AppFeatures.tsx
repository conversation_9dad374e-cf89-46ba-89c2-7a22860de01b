
import React, { useState, useCallback, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Check, Copy, UploadCloud, X, Loader2, Star, ThumbsUp, ThumbsDown } from 'lucide-react';
import type { SocialPlatformId, ToneId, GenerationResult, UploadedFile } from '../types';
import { SOCIAL_PLATFORMS, TONES } from '../constants';
import { FireLoader } from './LoadingSpinner';

export const SocialPlatformSelector: React.FC<{ selected: SocialPlatformId; onSelect: (id: SocialPlatformId) => void; }> = ({ selected, onSelect }) => (
    <div>
        <label className="block text-sm font-medium text-gray-300 mb-2">Plataforma</label>
        <div className="grid grid-cols-2 sm:grid-cols-4 gap-2">
            {SOCIAL_PLATFORMS.map(p => (
                <motion.button
                    key={p.id}
                    onClick={() => onSelect(p.id)}
                    className={`px-3 py-2 rounded-lg text-sm transition-all border justify-center items-center flex gap-2 ${selected === p.id ? `bg-gradient-to-r ${p.color} text-white border-white/20 shadow-lg` : 'bg-gray-800/50 text-gray-400 border-transparent hover:bg-gray-700/50'}`}
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                >
                    {p.icon}
                    <span className="font-medium">{p.label}</span>
                </motion.button>
            ))}
        </div>
    </div>
);

export const ToneSelector: React.FC<{ selected: ToneId; onSelect: (id: ToneId) => void; }> = ({ selected, onSelect }) => (
    <div>
        <label className="block text-sm font-medium text-gray-300 mb-2">Tom da Conversa</label>
        <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-5 gap-2">
            {TONES.map(t => (
                <motion.button
                    key={t.id}
                    onClick={() => onSelect(t.id)}
                    className={`px-3 py-2 rounded-lg text-sm transition-all border flex items-center justify-center gap-2 ${selected === t.id ? `bg-gradient-to-r ${t.color} text-white border-white/20 shadow-lg` : 'bg-gray-800/50 text-gray-400 border-transparent hover:bg-gray-700/50'}`}
                     whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                >
                    {t.icon}
                    <span className="font-medium">{t.label}</span>
                </motion.button>
            ))}
        </div>
    </div>
);

export const ImageUpload: React.FC<{ onUpload: (file: UploadedFile) => void; uploadedFile: UploadedFile | null; onRemove: () => void; title?: string; subtitle?: string; }> = ({ onUpload, uploadedFile, onRemove, title, subtitle }) => {
    const inputRef = useRef<HTMLInputElement>(null);
    const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
        const file = event.target.files?.[0];
        if (file) {
            const reader = new FileReader();
            reader.onloadend = () => {
                onUpload({ file, preview: reader.result as string });
            };
            reader.readAsDataURL(file);
        }
    };
    
    return (
        <div className="w-full">
            <input type="file" ref={inputRef} onChange={handleFileChange} accept="image/png, image/jpeg" className="hidden" />
            {!uploadedFile ? (
                <motion.div
                    className="w-full h-48 border-2 border-dashed border-gray-600 rounded-2xl flex flex-col items-center justify-center text-gray-400 cursor-pointer hover:border-orange-500 hover:text-orange-500 transition-colors"
                    onClick={() => inputRef.current?.click()}
                    whileHover={{ scale: 1.02 }}
                >
                    <UploadCloud className="w-12 h-12 mb-2" />
                    <p className="font-semibold">{title || 'Clique para enviar uma imagem'}</p>
                    <p className="text-sm">{subtitle || 'PNG ou JPG'}</p>
                </motion.div>
            ) : (
                <div className="relative w-full max-w-sm mx-auto">
                    <img src={uploadedFile.preview} alt="Preview" className="w-full rounded-xl object-contain max-h-64" />
                    <button onClick={onRemove} className="absolute -top-2 -right-2 bg-red-500 text-white p-1.5 rounded-full shadow-lg hover:scale-110 transition-transform">
                        <X className="w-4 h-4" />
                    </button>
                </div>
            )}
        </div>
    );
};

export const ResultDisplay: React.FC<{ results: GenerationResult[]; isLoading: boolean; onCopy: (text: string) => void; copiedText: string | null }> = ({ results, isLoading, onCopy, copiedText }) => {
    if (isLoading) {
        return (
            <div className="flex flex-col items-center justify-center text-center p-8 space-y-4">
                <Loader2 className="w-12 h-12 animate-spin text-orange-500" />
                <p className="text-lg font-semibold text-gray-300">Nossa IA está criando...</p>
                <p className="text-gray-400">Um momento, o fogo está esquentando!</p>
            </div>
        );
    }

    if (results.length === 0) return null;

    return (
        <div className="space-y-4">
            <h3 className="text-xl font-bold text-center text-gray-200">Respostas Geradas</h3>
            <AnimatePresence>
                {results.map((result, index) => (
                    <motion.div
                        key={result.id}
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        exit={{ opacity: 0 }}
                        transition={{ delay: index * 0.1 }}
                        className="p-4 bg-white/5 rounded-xl border border-white/10 group"
                    >
                        <p className="text-gray-200 mb-3">{result.text}</p>
                        <div className="flex justify-end items-center gap-2 opacity-0 group-hover:opacity-100 transition-opacity">
                            <button onClick={() => onCopy(result.text)} className="p-2 rounded-md hover:bg-white/10 text-gray-400 hover:text-white">
                                {copiedText === result.text ? <Check className="w-4 h-4 text-green-500"/> : <Copy className="w-4 h-4"/>}
                            </button>
                        </div>
                    </motion.div>
                ))}
            </AnimatePresence>
        </div>
    );
};
